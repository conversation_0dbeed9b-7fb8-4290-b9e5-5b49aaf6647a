(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{"2Rqu":function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",["PARAGRAPH"===e.survey.questions[e.index].quType||"PAGETAG"===e.survey.questions[e.index].quType?["PAGETAG"===e.survey.questions[e.index].quType?n("div",[n("div",{staticClass:"qu-catalogue-item qu-catalogue-page"},[e._v("分页：下一页("+e._s(e.pageNum)+"/"+e._s(e.pageSize)+")")])]):e._e(),e._v(" "),"PARAGRAPH"===e.survey.questions[e.index].quType?n("div",[n("div",{staticClass:"qu-catalogue-item qu-catalogue-paragraph"},[e._v("分段："+e._s(e.survey.questions[e.index].quTitleObj.dwText))])]):e._e()]:[n("div",{staticClass:"qu-catalogue-item qu-catalogue-other"},[e._v("Q"+e._s(e.quNum)+"、"+e._s(e.survey.questions[e.index].quTitleObj.dwText))])]],2)},s=[]},"49t4":function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{size:"mini"}},[n("div",[n("div",[n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.opoqAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.opoqAttr,"enabled",t)},expression:"survey.surveyAttrs.opoqAttr.enabled"}},[e._v("一页一题")])],1)])],1),e._v(" "),n("div",{staticClass:"dw-title-attr"},[e._v("回答限制")]),e._v(" "),n("div",{staticStyle:{"padding-left":"10px"}},[n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anBroAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anBroAttr,"enabled",t)},expression:"survey.surveyAttrs.anBroAttr.enabled"}},[e._v("启用浏览器终端回答限制")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anBroAttr.enabled,expression:"survey.surveyAttrs.anBroAttr.enabled"}],staticStyle:{"font-size":"12px"}},[e._v("\n            每个浏览器可回答次数"),n("el-input-number",{staticStyle:{width:"130px"},attrs:{min:1,max:1e5,size:"mini"},model:{value:e.survey.surveyAttrs.anBroAttr.anNum,callback:function(t){e.$set(e.survey.surveyAttrs.anBroAttr,"anNum",t)},expression:"survey.surveyAttrs.anBroAttr.anNum"}}),e._v(" 次\n          ")],1)]),e._v(" "),n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anIpAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anIpAttr,"enabled",t)},expression:"survey.surveyAttrs.anIpAttr.enabled"}},[e._v("启用IP回答限制")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anIpAttr.enabled,expression:"survey.surveyAttrs.anIpAttr.enabled"}],staticStyle:{"font-size":"12px"}},[e._v("\n            每个IP可回答次数"),n("el-input-number",{staticStyle:{width:"130px"},attrs:{min:1,max:1e5,size:"mini"},model:{value:e.survey.surveyAttrs.anIpAttr.anNum,callback:function(t){e.$set(e.survey.surveyAttrs.anIpAttr,"anNum",t)},expression:"survey.surveyAttrs.anIpAttr.anNum"}}),e._v(" 次\n          ")],1)]),e._v(" "),n("el-checkbox",{model:{value:e.survey.surveyAttrs.anRefreshAttr.randomCode,callback:function(t){e.$set(e.survey.surveyAttrs.anRefreshAttr,"randomCode",t)},expression:"survey.surveyAttrs.anRefreshAttr.randomCode"}},[e._v("重复回答启用验证码")]),e._v(" "),n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anPwdAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anPwdAttr,"enabled",t)},expression:"survey.surveyAttrs.anPwdAttr.enabled"}},[e._v("启用通过密码答卷")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anPwdAttr.enabled,expression:"survey.surveyAttrs.anPwdAttr.enabled"}]},[n("el-input",{staticStyle:{width:"160px"},attrs:{size:"mini",placeholder:"请输入密码"},model:{value:e.survey.surveyAttrs.anPwdAttr.anPwdCode,callback:function(t){e.$set(e.survey.surveyAttrs.anPwdAttr,"anPwdCode",t)},expression:"survey.surveyAttrs.anPwdAttr.anPwdCode"}})],1)])],1)]),e._v(" "),n("div",{staticStyle:{"margin-top":"20px"}},[n("div",{staticClass:"dw-title-attr"},[e._v("计分属性")]),e._v(" "),n("div",{staticStyle:{"padding-left":"10px"}},[n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.scoreAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.scoreAttr,"enabled",t)},expression:"survey.surveyAttrs.scoreAttr.enabled"}},[e._v("打开计分功能")])],1)]),e._v(" "),n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.scoreAttr.showSumScore.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.scoreAttr.showSumScore,"enabled",t)},expression:"survey.surveyAttrs.scoreAttr.showSumScore.enabled"}},[e._v("答卷结束显示总分")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.scoreAttr.showSumScore.enabled,expression:"survey.surveyAttrs.scoreAttr.showSumScore.enabled"}]},[n("el-select",{model:{value:e.survey.surveyAttrs.scoreAttr.showSumScore.showContent,callback:function(t){e.$set(e.survey.surveyAttrs.scoreAttr.showSumScore,"showContent",t)},expression:"survey.surveyAttrs.scoreAttr.showSumScore.showContent"}},[n("el-option",{attrs:{value:"sum",label:"仅显示总分"}}),e._v(" "),n("el-option",{attrs:{value:"sumAfterDetail",label:"先显示总分后显示详情"}}),e._v(" "),n("el-option",{attrs:{value:"sumAndDetail",label:"直接显示总分与详细"}})],1)],1)])],1)]),e._v(" "),n("div",{staticStyle:{"margin-top":"20px"}},[n("div",{staticClass:"dw-title-attr"},[e._v("开始结束")]),e._v(" "),n("div",{staticStyle:{"padding-left":"10px"}},[n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anEndNumAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anEndNumAttr,"enabled",t)},expression:"survey.surveyAttrs.anEndNumAttr.enabled"}},[e._v("指定结束份数")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anEndNumAttr.enabled,expression:"survey.surveyAttrs.anEndNumAttr.enabled"}]},[n("el-input-number",{staticStyle:{width:"160px"},attrs:{min:1,max:1e5,size:"mini"},model:{value:e.survey.surveyAttrs.anEndNumAttr.endNum,callback:function(t){e.$set(e.survey.surveyAttrs.anEndNumAttr,"endNum",t)},expression:"survey.surveyAttrs.anEndNumAttr.endNum"}})],1)]),e._v(" "),n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anStartTimeAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anStartTimeAttr,"enabled",t)},expression:"survey.surveyAttrs.anStartTimeAttr.enabled"}},[e._v("指定开始时间")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anStartTimeAttr.enabled,expression:"survey.surveyAttrs.anStartTimeAttr.enabled"}]},[n("el-date-picker",{staticStyle:{width:"160px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetime",placeholder:"选择日期时间"},model:{value:e.survey.surveyAttrs.anStartTimeAttr.startTime,callback:function(t){e.$set(e.survey.surveyAttrs.anStartTimeAttr,"startTime",t)},expression:"survey.surveyAttrs.anStartTimeAttr.startTime"}})],1)]),e._v(" "),n("el-form-item",[n("div",[n("el-checkbox",{model:{value:e.survey.surveyAttrs.anEndTimeAttr.enabled,callback:function(t){e.$set(e.survey.surveyAttrs.anEndTimeAttr,"enabled",t)},expression:"survey.surveyAttrs.anEndTimeAttr.enabled"}},[e._v("指定结束时间")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.survey.surveyAttrs.anEndTimeAttr.enabled,expression:"survey.surveyAttrs.anEndTimeAttr.enabled"}]},[n("el-date-picker",{staticStyle:{width:"160px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetime",placeholder:"选择日期时间"},model:{value:e.survey.surveyAttrs.anEndTimeAttr.endTime,callback:function(t){e.$set(e.survey.surveyAttrs.anEndTimeAttr,"endTime",t)},expression:"survey.surveyAttrs.anEndTimeAttr.endTime"}})],1)])],1)])])],1)},s=[]},"4T9c":function(e,t,n){},"64Vg":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n("mqu2"),s=n("VeDC"),a=o(n("t2rG")),i=o(n("lIVj"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignQuBanks",components:{DwDesignQuBankQuestion:i.default,draggable:a.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{activeName:0,quBanks:[]}},mounted:function(){this.loadQuBanks()},methods:{loadQuBanks:function(){var e=this;(0,r.bankQuestions)().then(function(t){console.debug("bankQuestions-response"),console.debug(t);var n=t.data;if(n.hasOwnProperty("resultCode")&&200===n.resultCode){var r=n.data;r.map(function(e,t){e.questions=(0,s.parseQuestions)(e.questions,!1)}),e.quBanks=r,e.loading=!1}})},onStart:function(){this.drag=!0,this.$emit("start-drag-right")},onEnd:function(){this.$emit("end-drag"),this.drag=!1}}}},"7Aei":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=m(n("t2rG")),s=m(n("nMAJ")),a=m(n("M1ne")),i=m(n("cwsm")),o=m(n("OjaA")),u=m(n("NUBw")),l=m(n("DrJ/")),d=m(n("PIVs")),v=m(n("NOWu")),c=n("zcjV"),y=m(n("5VHg")),f=m(n("KWo8")),p=m(n("Voeh"));function m(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignLeftRightLayout",components:{DwDesignHeader:p.default,DwDesignToolbarTop:f.default,DwDesignToolbar:y.default,DwFooter:v.default,DwDesignContainerBodyRight:d.default,DwDesignContainerBodyLeft:l.default,DwDesignContainerBodyCenter:u.default,DwTextEditLabelCommon:o.default,DwDesignQuRadio:i.default,DwTextEditLabel:a.default,DwDesignQuestion:s.default,draggable:r.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{surveyId:"",drag:!1,headerQuToolbarStyle:{top:60,index:200},containerLRStyle:{top:110,index:100},lrContentHeight:100,radio:"1",hover:!1}},mounted:function(){window.addEventListener("scroll",this.onScroll),this.onScroll()},methods:{onStart:function(){this.drag=!0},onEnd:function(){this.drag=!1,this.resetStyleIndex()},resetStyleIndex:function(){this.headerQuToolbarStyle.index=200,this.containerLRStyle.index=100},onStartToolbar:function(){this.onStart(),this.resetStyleIndex()},onStartDragContainer:function(){this.onStart(),this.headerQuToolbarStyle.index=20,this.containerLRStyle.index=10},onStartRight:function(){this.onStart(),this.headerQuToolbarStyle.index=100,this.containerLRStyle.index=200},onScroll:function(){var e=document.documentElement.scrollTop||document.body.scrollTop;if(e>=60)this.headerQuToolbarStyle.top="0px",this.containerLRStyle.top="50px",this.lrContentHeight=window.innerHeight-50-60;else{var t=60-e;this.headerQuToolbarStyle.top=t+"px";var n=50+t;this.containerLRStyle.top=n+"px",this.lrContentHeight=window.innerHeight-n-60}},documentClick:function(){(0,c.resetOtherClickItem)(this.survey,-1)}}}},"7Y/P":function(e,t,n){"use strict";n.r(t);var r=n("XYMP"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("T7H7"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("8cIW")},"data-v-7345e011",null);t.default=u.exports},"88fk":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n("nMAJ")),s=i(n("nNty")),a=i(n("TP7P"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignQuBankQuestion",components:{DwDesignQuestionModelCommon:a.default,DwAnswerQuestion:s.default,DwDesignQuestion:r.default},props:{item:{type:Object,default:function(){}}},data:function(){return{}}}},"8CH3":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=u(n("gDS+")),s=n("CG6M"),a=u(n("t2rG")),i=n("KSwC"),o=n("iYnu");function u(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignToolbarTop",components:{draggable:a.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{questions:[],questions1:[],tabs:[],drag:!1,loading:!0,isAutoSave:!0,autoSaveTime:30,intervalSaveSurvey:null,prevPath:"/v6"}},mounted:function(){var e=this;this.autoSaveSurvey(),o.dwFooterUtils.isLayoutLr(function(t){e.prevPath="/v6/lr"})},beforeDestroy:function(){this.stopIntervalSaveSurvey()},methods:{autoSaveSurvey:function(){this.isAutoSave?this.startIntervalSaveSurvey():this.stopIntervalSaveSurvey()},startIntervalSaveSurvey:function(){var e=this;this.autoSaveTime=20,this.intervalSaveSurvey=setInterval(function(){e.saveSurveyFun(null),e.autoSaveTime=20,console.log("每隔20秒执行一次自动保存")},2e4),this.intervalSaveSurveyTime=setInterval(function(){e.autoSaveTime=e.autoSaveTime-1,e.autoSaveTime<=0&&(e.autoSaveTime=0)},1e3)},stopIntervalSaveSurvey:function(){null!==this.intervalSaveSurvey&&clearInterval(this.intervalSaveSurvey),null!==this.intervalSaveSurveyTime&&clearInterval(this.intervalSaveSurveyTime)},setSurvey:function(){var e=this;this.stopIntervalSaveSurvey(),this.saveSurveyFun(function(){var t=e.$route.params.dwSurveyId;e.$router.push(e.prevPath+"/dw/survey/c/attr/"+t)})},saveSurvey:function(){this.saveSurveyFun(null)},devSurvey:function(){this.stopIntervalSaveSurvey(),this.previewSurvey()},clickDesignStyle:function(e,t){return"designStyle"!==e||(this.previewSurvey(),!1)},previewSurvey:function(){var e=this;this.saveSurveyFun(function(){var t=e.$route.params.dwSurveyId;e.$router.push("/v6/diaowen/dw-preview-style/survey/"+t)})},saveSurveyFun:function(e){var t=this,n=this.survey.id,a=this.survey.sid;(0,i.clearSurveyJson)(this.survey),console.debug("save this.survey",this.survey);var o=(0,r.default)((0,i.getSaveSurveyJsonText)(this.survey)),u={surveyId:n,sid:a,surveyJsonText:o,surveyJsonSimple:(0,r.default)((0,i.getSurveyJsonSimple)(o))};console.debug("surveyJson data",u),(0,s.dwSaveSurveyJson)(u).then(function(n){console.debug("dwSaveSurveyJson-response",n);var r=n.data;r.hasOwnProperty("resultCode")&&200===r.resultCode?(t.$message({message:"保存成功！",type:"success"}),null!=e&&e()):t.$message.error("保存失败！")})}}}},"8cIW":function(e,t,n){},"9ten":function(e,t,n){},AEad:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=v(n("t2rG")),s=n("CG6M"),a=v(n("nMAJ")),i=v(n("M1ne")),o=v(n("cwsm")),u=v(n("OjaA")),l=v(n("7Y/P")),d=v(n("tsEo"));function v(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignSurveyCore",components:{DwDesignLeftRightLayout:d.default,DwDesignTopBottomLayout:l.default,DwTextEditLabelCommon:u.default,DwDesignQuRadio:o.default,DwTextEditLabel:i.default,DwDesignQuestion:a.default,draggable:r.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{surveyId:"",drag:!1,headerQuToolbarStyle:"",containerLRStyle:"",questions:[],radio:"1",hover:!1}},mounted:function(){this.loadDesignSurveyData(),window.addEventListener("scroll",this.onScroll)},methods:{onStart:function(){this.drag=!0},onEnd:function(){this.drag=!1},loadDesignSurveyData:function(){var e=this;(0,s.questionComps)().then(function(t){console.debug("response"),console.debug(t);var n=t.data;200===n.resultCode&&(e.questions=n.data)})},onScroll:function(e){var t=document.documentElement.scrollTop||document.body.scrollTop;if(t>=60){this.headerQuToolbarStyle="top:0px;";var n=t-60;this.containerLRStyle="top:"+n+"px;"}else{var r=60-t;this.headerQuToolbarStyle="top:"+r+"px;",this.containerLRStyle="top:0px;"}},documentClick:function(){}}}},AOjs:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{on:{click:function(t){return t.stopPropagation(),e.clickShowPopoverEvent.apply(null,arguments)}}},[n("el-tabs",{attrs:{type:"border-card"},model:{value:e.survey.surveyFocusObj.rightFocusTab,callback:function(t){e.$set(e.survey.surveyFocusObj,"rightFocusTab",t)},expression:"survey.surveyFocusObj.rightFocusTab"}},[n("el-tab-pane",{attrs:{name:"surveySet",label:"问卷设置"}},[n("div",{staticClass:"scrollable-hidden scrollable-y",style:"height:"+e.lrContentHeight+"px;"},[n("div",{staticStyle:{"padding-bottom":"5px"}},[n("el-alert",{attrs:{title:"可以配置整个问卷的基本属性",type:"info","show-icon":""}})],1),e._v(" "),n("dw-design-survey-attrs",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)]),e._v(" "),n("el-tab-pane",{attrs:{name:"quSet",label:"题目设置"}},[n("dw-qu-attrs",{attrs:{index:e.survey.surveyFocusObj.focusQuIndex},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)],1)],1)},s=[]},AnAG:function(e,t,n){"use strict";n.r(t);var r=n("wIaR"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("SM+r"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,null,null,null);t.default=u.exports},"DrJ/":function(e,t,n){"use strict";n.r(t);var r=n("kS9L"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("y48y"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("npSE")},"data-v-624ff488",null);t.default=u.exports},H50A:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("dw-design-question-model-common",{attrs:{item:e.item}},[n("div",{staticClass:"dw-list-group-item-content qu-bank-question-content"},[e.item.hasOwnProperty("dwQuIcon")&&null!==e.item.dwQuIcon&&void 0!==e.item.dwQuIcon?n("div",{staticClass:"dw-list-group-item-in"},[n("span",{domProps:{innerHTML:e._s(e.item.dwQuIcon)}}),e._v(" "+e._s(e.item.quName)+" "),n("span",{staticClass:"dw-list-group-item-in-type"})]):n("div",{staticClass:"dw-list-group-item-in"},[e._v(" "+e._s(e.item.quName)+" "),n("span",{staticClass:"dw-list-group-item-in-type"},[e._v("["+e._s(e.item.quTypeName)+"]")])])])])],1)},s=[]},ImRL:function(e,t,n){},J8O9:function(e,t,n){"use strict";n.r(t);var r=n("LWFk"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("N2Q9"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("ImRL")},"data-v-55fdd826",null);t.default=u.exports},KWo8:function(e,t,n){"use strict";n.r(t);var r=n("8CH3"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("yBqq"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("gNpF")},"data-v-14f41221",null);t.default=u.exports},LWFk:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=c(n("gDS+")),s=n("CG6M"),a=c(n("t2rG")),i=n("VeDC"),o=c(n("nMAJ")),u=n("KSwC"),l=c(n("nNty")),d=n("wWYR"),v=c(n("lIVj"));function c(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignToolbarLeft",components:{DwDesignQuBankQuestion:v.default,DwAnswerQuestion:l.default,DwDesignQuestion:o.default,draggable:a.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{questions:[],questions1:[],tabs:[],drag:!1,loading:!0,isAutoSave:!0,autoSaveTime:30,intervalSaveSurvey:null,activeName:[0,1,2,3]}},mounted:function(){this.loadDesignSurveyData(),this.autoSaveSurvey()},beforeDestroy:function(){this.stopIntervalSaveSurvey()},methods:{clickToolbarItem:function(e){void 0!==e.eventName&&null!==e.eventName||(e.isNew=!0,this.survey.questions.push((0,d.dwResetQuestionRefreshValue)(JSON.parse((0,r.default)(e)))),(0,i.resetQuestion)(this.survey.questions[this.survey.questions.length-1]))},onStart:function(){this.drag=!0,this.$emit("start-drag")},onEnd:function(){this.$emit("end-drag"),this.drag=!1},loadDesignSurveyData:function(){var e=this;(0,s.questionComps)().then(function(t){console.debug("questionComps-response"),console.debug(t);var n=t.data;if(n.hasOwnProperty("resultCode")&&200===n.resultCode){var r=n.data;r.map(function(e,t){e.tabQus.map(function(e,t){e.questions=(0,i.initQuestionModels)((0,i.parseQuestions)(e.questions,!1))})}),e.tabs=r,e.loading=!1}})},autoSaveSurvey:function(){this.isAutoSave?this.startIntervalSaveSurvey():this.stopIntervalSaveSurvey()},startIntervalSaveSurvey:function(){var e=this;this.autoSaveTime=20,this.intervalSaveSurvey=setInterval(function(){e.saveSurveyFun(null),e.autoSaveTime=20,console.log("每隔20秒执行一次自动保存")},2e4),this.intervalSaveSurveyTime=setInterval(function(){e.autoSaveTime=e.autoSaveTime-1,e.autoSaveTime<=0&&(e.autoSaveTime=0)},1e3)},stopIntervalSaveSurvey:function(){null!==this.intervalSaveSurvey&&clearInterval(this.intervalSaveSurvey),null!==this.intervalSaveSurveyTime&&clearInterval(this.intervalSaveSurveyTime)},saveSurvey:function(){this.saveSurveyFun(null)},devSurvey:function(){this.stopIntervalSaveSurvey(),this.previewSurvey()},clickDesignStyle:function(e,t){return"designStyle"!==e||(this.previewSurvey(),!1)},previewSurvey:function(){var e=this;this.saveSurveyFun(function(){var t=e.$route.params.dwSurveyId;e.$router.push("/v6/diaowen/dw-preview/survey/"+t)})},saveSurveyFun:function(e){var t=this,n=this.survey.id,a=this.survey.sid;(0,u.clearSurveyJson)(this.survey),console.debug("save this.survey",this.survey);var i=(0,r.default)((0,u.getSaveSurveyJsonText)(this.survey)),o={surveyId:n,sid:a,surveyJsonText:i,surveyJsonSimple:(0,r.default)((0,u.getSurveyJsonSimple)(i))};console.debug("surveyJson data",o),(0,s.dwSaveSurveyJson)(o).then(function(n){console.debug("dwSaveSurveyJson-response",n);var r=n.data;r.hasOwnProperty("resultCode")&&200===r.resultCode?(t.$message({message:"保存成功！",type:"success"}),null!=e&&e()):t.$message.error("保存失败！")})}}}},N2Q9:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.tabs.length>0?n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[n("div",{staticStyle:{"padding-bottom":"5px"}},[n("el-alert",{attrs:{title:"点击或拖动可加入到问卷中",type:"info","show-icon":""}})],1),e._v(" "),n("el-collapse",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},e._l(e.tabs,function(t,r){return n("el-collapse-item",{key:"quBank_"+r,attrs:{title:t.tabName,name:r}},[e._l(t.tabQus,function(t,r){return t.questions.length>0?n("div",{key:"toolbar"+r,staticClass:"tools_item"},[n("div",{staticClass:"quBanks"},[void 0===t.eventType||null===t.eventType?[n("div",[n("div",{staticClass:"toolbars-text",staticStyle:{"border-top":"none","text-align":"left","padding-left":"10px"}},[e._v(e._s(t.tabQuName))])]),e._v(" "),n("draggable",{staticClass:"toolbars-draggable",attrs:{group:{name:"questionGroup",pull:"clone",put:!1},sort:!1,"force-fallback":!0,animation:"300","drag-class":"dragClass","ghost-class":"ghostClass","chosen-class":"chosenClass"},on:{start:e.onStart,end:e.onEnd},model:{value:t.questions,callback:function(n){e.$set(t,"questions",n)},expression:"item.questions"}},[n("transition-group",{staticClass:"dw-list-group dw-grid"},e._l(t.questions,function(t,r){return n("div",{key:"base"+r,staticClass:"dw-list-group-item",on:{click:function(n){return n.stopPropagation(),e.clickToolbarItem(t)}}},[n("dw-design-qu-bank-question",{attrs:{item:t}})],1)}),0)],1)]:void 0],2)]):e._e()})],2)}),1)],1):e._e()},s=[]},PIVs:function(e,t,n){"use strict";n.r(t);var r=n("VW+h"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("AOjs"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,null,null,null);t.default=u.exports},"SM+r":function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[n("dw-design-survey-core",{staticClass:"user-select-none",model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)},s=[]},SdOv:function(e,t,n){"use strict";n.r(t);var r=n("64Vg"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("X8GC"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("9ten")},"data-v-0c8f1627",null);t.default=u.exports},T7H7:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{on:{click:e.documentClick}},[null!=e.survey?n("div",{staticClass:"dw-design-container"},[n("el-container",[n("el-header",{staticClass:"header"},[n("dw-design-header",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("el-main",{staticStyle:{padding:"0"}},[n("div",{staticStyle:{"min-height":"600px"}},[n("div",[n("div",{ref:"toolsWrap",style:"top:"+e.headerQuToolbarStyle.top+";z-index: "+e.headerQuToolbarStyle.index+";",attrs:{id:"tools_wrap"}},[n("dw-design-toolbar",{on:{"start-drag":e.onStartToolbar,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("div",{staticStyle:{"margin-top":"157px","margin-bottom":"0"},style:e.containerBodyStyle},[n("div",{staticClass:"dw-container-body"},[n("el-row",{staticStyle:{margin:"0"},attrs:{gutter:10}},[n("el-col",{attrs:{span:4}},[n("div",{staticClass:"dw-container-body-center-left dw-container-body-lr",style:"top:"+e.containerLRStyle.top+";z-index: "+e.containerLRStyle.index+";"},[n("dw-design-container-body-left",{attrs:{"lr-content-height":e.lrContentHeight},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)]),e._v(" "),n("el-col",{attrs:{span:16}},[n("dw-design-container-body-center",{ref:"designContainerBody",on:{"start-drag-container":e.onStartDragContainer,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("el-col",{attrs:{span:4}},[n("div",{staticClass:"dw-container-body-center-right dw-container-body-lr",style:"top:"+e.containerLRStyle.top+";z-index: "+e.containerLRStyle.index+";"},[n("dw-design-container-body-right",{attrs:{"lr-content-height":e.lrContentHeight},on:{"start-drag-right":e.onStartRight,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)])],1)],1)])])]),e._v(" "),n("div",{},[n("dw-footer")],1)])],1)],1):n("div",[n("div",{staticStyle:{"font-size":"16px","text-align":"center",padding:"100px",height:"100vh","background-color":"white",color:"#202120"}},[e._v("\n      加载中...\n    ")])])])},s=[]},"VW+h":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n("SdOv")),s=i(n("l/iK")),a=i(n("AbpU"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignContainerBodyRight",components:{DwQuAttrs:a.default,DwDesignSurveyAttrs:s.default,DwDesignQuBanks:r.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}},lrContentHeight:{type:Number,default:function(){}}},methods:{onStartRight:function(){this.drag=!0,this.$emit("start-drag-right")},onEnd:function(){this.$emit("end-drag"),this.drag=!1},clickShowPopoverEvent:function(){}}}},WEnS:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("dw-design-survey-main")],1)},s=[]},WPvs:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"DwDesignSurveyAttrs",model:{prop:"survey",event:"update-survey"},props:{index:{type:Number,default:0},survey:{type:Object,default:function(){}}},data:function(){return{labelPosition:"right"}},methods:{}}},X8GC:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-collapse",{attrs:{accordion:""},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},e._l(e.quBanks,function(t,r){return n("el-collapse-item",{key:"quBank_"+r,attrs:{title:t.tabQuName,name:r}},[n("div",{staticStyle:{"padding-bottom":"5px"}},[n("el-alert",{attrs:{title:"点击或拖动可加入到问卷中",type:"info","show-icon":""}})],1),e._v(" "),[n("div",{staticClass:"quBanks"},[n("draggable",{staticClass:"toolbars-draggable",attrs:{group:{name:"questionGroup",pull:"clone",put:!1},sort:!1,"force-fallback":!0,animation:"300","drag-class":"dragClass","ghost-class":"ghostClass","chosen-class":"chosenClass"},on:{start:e.onStart,end:e.onEnd},model:{value:t.questions,callback:function(n){e.$set(t,"questions",n)},expression:"item.questions"}},[n("transition-group",{staticClass:"dw-list-group dw-grid"},e._l(t.questions,function(e,t){return n("div",{key:"quBankQu_"+t,staticClass:"dw-list-group-item"},[n("dw-design-qu-bank-question",{attrs:{item:e}})],1)}),0)],1)],1)]],2)}),1)],1)},s=[]},XYMP:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=p(n("t2rG")),s=p(n("nMAJ")),a=p(n("M1ne")),i=p(n("cwsm")),o=p(n("OjaA")),u=p(n("Voeh")),l=p(n("5VHg")),d=p(n("NUBw")),v=p(n("DrJ/")),c=p(n("PIVs")),y=p(n("NOWu")),f=n("zcjV");function p(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignTopBottomLayout",components:{DwFooter:y.default,DwDesignContainerBodyRight:c.default,DwDesignContainerBodyLeft:v.default,DwDesignContainerBodyCenter:d.default,DwDesignToolbar:l.default,DwDesignHeader:u.default,DwTextEditLabelCommon:o.default,DwDesignQuRadio:i.default,DwTextEditLabel:a.default,DwDesignQuestion:s.default,draggable:r.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}}},data:function(){return{surveyId:"",drag:!1,headerQuToolbarStyle:{top:60,index:200},containerLRStyle:{top:0,index:100},lrContentHeight:157,containerBodyStyle:"",questions:[],radio:"1",hover:!1}},mounted:function(){window.addEventListener("scroll",this.onScroll),this.onScroll()},beforeDestroy:function(){window.removeEventListener("scroll",this.onScroll)},methods:{onStart:function(){this.drag=!0},onEnd:function(){this.drag=!1,this.resetStyleIndex()},resetStyleIndex:function(){this.headerQuToolbarStyle.index=200,this.containerLRStyle.index=100},onStartToolbar:function(){this.onStart(),this.resetStyleIndex()},onStartDragContainer:function(){this.onStart(),this.headerQuToolbarStyle.index=20,this.containerLRStyle.index=10},onStartRight:function(){this.onStart(),this.headerQuToolbarStyle.index=100,this.containerLRStyle.index=200},centerMarginTop:function(){var e=157;return window.innerHeight>1280&&(e=160),this.containerBodyStyle="margin-top:"+e+"px;",e},onScroll:function(){var e=document.documentElement.scrollTop||document.body.scrollTop,t=this.centerMarginTop();if(e>=60){var n=e-60,r=window.innerHeight-t-60;console.debug("lrHeight",r),this.headerQuToolbarStyle.top="0px",this.containerLRStyle.top=n+"px",this.lrContentHeight=r}else{var s=60-e;console.debug("window.innerHeight",window.innerHeight);var a=window.innerHeight-(t+s)-60;this.headerQuToolbarStyle.top=s+"px",this.containerLRStyle.top="0px",this.lrContentHeight=a}},documentClick:function(){(0,f.resetOtherClickItem)(this.survey,-1)}}}},asv5:function(e,t,n){"use strict";n.r(t);var r=n("AEad"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("lV5w"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("sGe4")},"data-v-522d67cd",null);t.default=u.exports},c4UN:function(e,t,n){},"e/OP":function(e,t,n){"use strict";n.r(t);var r=n("ld5F"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("2Rqu"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("c4UN")},"data-v-8963bbe2",null);t.default=u.exports},gNpF:function(e,t,n){},iGfd:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{on:{click:e.documentClick}},[null!=e.survey?n("div",{staticClass:"dw-design-container"},[n("el-container",[n("el-header",{staticClass:"header"},[n("dw-design-header",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("el-main",{staticStyle:{padding:"0"}},[n("div",{staticStyle:{"min-height":"600px"}},[n("div",[n("div",{ref:"toolsWrap",style:"top:"+e.headerQuToolbarStyle.top+";z-index: "+e.headerQuToolbarStyle.index+";",attrs:{id:"tools_wrap"}},[n("dw-design-toolbar-top",{on:{"start-drag":e.onStartToolbar,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("div",{staticStyle:{"margin-top":"50px","margin-bottom":"0"}},[n("div",{staticClass:"dw-container-body"},[n("el-row",{staticStyle:{margin:"0"},attrs:{gutter:10}},[n("el-col",{attrs:{span:4}},[n("div",{staticClass:"dw-container-body-center-left dw-container-body-lr dw-container-body-lr-flex",style:"top:"+e.containerLRStyle.top+";z-index: "+e.containerLRStyle.index+";"},[n("dw-design-container-body-left",{attrs:{"lr-content-height":e.lrContentHeight},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)]),e._v(" "),n("el-col",{attrs:{span:16,offset:4}},[n("dw-design-container-body-center",{ref:"designContainerBody",on:{"start-drag-container":e.onStartDragContainer,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1),e._v(" "),n("el-col",{attrs:{span:4}},[n("div",{staticClass:"dw-container-body-center-right dw-container-body-lr dw-container-body-lr-flex",style:"top:"+e.containerLRStyle.top+";z-index: "+e.containerLRStyle.index+";"},[n("dw-design-container-body-right",{attrs:{"lr-content-height":e.lrContentHeight},on:{"start-drag-right":e.onStartRight,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)])],1)],1)])])]),e._v(" "),n("div",{},[n("dw-footer")],1)])],1)],1):n("div",[n("div",{staticStyle:{"font-size":"16px","text-align":"center",padding:"100px",height:"100vh","background-color":"white",color:"#202120"}},[e._v("\n      加载中...\n    ")])])])},s=[]},kS9L:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=d(n("gDS+")),s=d(n("t2rG")),a=n("wWYR"),i=d(n("e/OP")),o=d(n("l/iK")),u=d(n("J8O9")),l=d(n("SdOv"));function d(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwDesignContainerBodyLeft",components:{DwDesignQuBanks:l.default,DwDesignToolbarLeft:u.default,DwDesignSurveyAttrs:o.default,DwDesignCatalogQuItem:i.default,draggable:s.default},model:{prop:"survey",event:"update-survey"},props:{survey:{type:Object,default:function(){}},lrContentHeight:{type:Number,default:function(){}}},data:function(){return{contentStyle:"",drag:!1}},mounted:function(){},methods:{onAdd:function(e){console.debug("left onAdd attrs",e);var t=e.newIndex;this.refreshData(t),this.survey.questions[t].quTitleObj.isNew=!0},onStart:function(){this.drag=!0},onEnd:function(e){this.drag=!1;var t=e.newIndex,n=e.oldIndex;t>n?this.refreshData(n):this.refreshData(t)},onScroll:function(){var e=document.documentElement.scrollTop||document.body.scrollTop;if(e>=60){var t=window.innerHeight-217-20;this.contentStyle="height:"+t+"px;"}else{var n=60-e,r=window.innerHeight-(217+n)-20;this.contentStyle="height:"+r+"px;"}},refreshData:function(e){var t=this;this.survey.questions.forEach(function(n,s){s>=e&&t.survey.questions.splice(s,1,(0,a.dwResetQuestionRefreshValue)(JSON.parse((0,r.default)(n))))})}}}},"l/iK":function(e,t,n){"use strict";n.r(t);var r=n("WPvs"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("49t4"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("pR1U")},"data-v-c5cffd68",null);t.default=u.exports},lIVj:function(e,t,n){"use strict";n.r(t);var r=n("88fk"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("H50A"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("4T9c")},"data-v-389529ac",null);t.default=u.exports},lV5w:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return null!=e.survey?n("div",[e.survey.hasOwnProperty("designLayout")&&"TB"===e.survey.designLayout?n("div",[n("dw-design-top-bottom-layout",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1):n("div",[n("dw-design-left-right-layout",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)]):e._e()},s=[]},ld5F:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"DwDesignCatalogQuItem",model:{prop:"survey",event:"update-survey"},props:{index:{type:Number,default:0},survey:{type:Object,default:function(){}}},computed:{quNum:function(){var e=this,t=0;return this.survey.questions.forEach(function(n,r){"PAGETAG"!==n.quType&&"PARAGRAPH"!==n.quType&&r<=e.index&&t++}),t},pageSize:function(){var e=1;return this.survey.questions.forEach(function(t,n){"PAGETAG"===t.quType&&e++}),e},pageNum:function(){var e=this,t=0;return this.survey.questions.forEach(function(n,r){"PAGETAG"===n.quType&&r<=e.index&&t++}),t}}}},likt:function(e,t,n){},mqu2:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bankQuestions=function(){return(0,s.default)({url:"/api/dwsurvey/app/v6/dw-design-survey/bank-qus.do",method:"get"})};var r,s=(r=n("t3Un"))&&r.__esModule?r:{default:r}},nNYl:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,s=(r=n("AnAG"))&&r.__esModule?r:{default:r};t.default={name:"DwSurveyDesignContent",components:{DwDesignSurveyMain:s.default}}},npSE:function(e,t,n){},pR1U:function(e,t,n){},sGe4:function(e,t,n){},tsEo:function(e,t,n){"use strict";n.r(t);var r=n("7Aei"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("iGfd"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,function(e){n("likt")},"data-v-9fcde55a",null);t.default=u.exports},wIaR:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,s=(r=n("asv5"))&&r.__esModule?r:{default:r},a=n("heCB");t.default={name:"DwDesignSurveyMain",components:{DwDesignSurveyCore:s.default},data:function(){return{loading:!0,survey:null,surveyJsonText:null}},mounted:function(){this.loadSurvey()},methods:{loadSurvey:function(){var e=this,t={surveyId:this.$route.params.dwSurveyId};(0,a.getDesignSurveyJsonBySurveyId)(t,function(t){console.debug("design survey",t),e.survey=t,e.loading=!1})}}}},y48y:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-tabs",{attrs:{type:"border-card"}},[e.survey.hasOwnProperty("designLayout")&&"LR"===e.survey.designLayout?n("el-tab-pane",{attrs:{label:"题型"}},[n("div",{staticClass:"scrollable-hidden scrollable-y",staticStyle:{"overflow-y":"scroll"},style:"height:"+e.lrContentHeight+"px;"},[n("dw-design-toolbar-left",{model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)]):e._e(),e._v(" "),n("el-tab-pane",{attrs:{label:"大纲"}},[n("div",{staticClass:"scrollable-hidden scrollable-y",staticStyle:{"overflow-y":"scroll"},style:"height:"+e.lrContentHeight+"px;"},[n("div",{staticClass:"dw_left_ul"},[n("div",{staticStyle:{"padding-bottom":"5px"}},[n("el-alert",{attrs:{title:"拖动目录可修改题目排序",type:"info","show-icon":""}})],1),e._v(" "),n("draggable",{attrs:{"force-fallback":!0,group:{name:"questionGroup",pull:!1,put:!1},animation:"300","drag-class":"dwDragClass","ghost-class":"dwGhostClass","chosen-class":"dwChosenClass"},on:{onAdd:e.onAdd,start:e.onStart,end:e.onEnd},model:{value:e.survey.questions,callback:function(t){e.$set(e.survey,"questions",t)},expression:"survey.questions"}},[n("transition-group",e._l(e.survey.questions,function(t,r){return n("div",{key:"surveyQu"+r},[n("dw-design-catalog-qu-item",{attrs:{index:r},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)}),0)],1)],1)])]),e._v(" "),n("el-tab-pane",{attrs:{label:"题库"}},[n("div",{staticClass:"scrollable-hidden scrollable-y",style:"height:"+e.lrContentHeight+"px;"},[n("dw-design-qu-banks",{on:{"start-drag-right":e.onStartRight,"end-drag":e.onEnd},model:{value:e.survey,callback:function(t){e.survey=t},expression:"survey"}})],1)])],1)],1)},s=[]},yBqq:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{padding:"6px 0"}},[n("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[n("el-col",{attrs:{span:18}},[n("el-button",{staticStyle:{padding:"5px"},attrs:{type:"text",size:"mini"},on:{click:e.devSurvey}},[n("i",{staticClass:"fa-regular fa-pen-to-square"}),e._v(" 题目编辑")]),e._v(" "),n("el-button",{staticStyle:{padding:"5px",color:"#575757"},attrs:{type:"text",size:"mini"},on:{click:e.devSurvey}},[n("i",{staticClass:"fa-solid fa-palette"}),e._v(" 样式设计")]),e._v(" "),n("el-button",{staticStyle:{padding:"5px",color:"#575757"},attrs:{type:"text",size:"mini"},on:{click:e.setSurvey}},[n("i",{staticClass:"fa-solid fa-gear"}),e._v(" 问卷设置")])],1),e._v(" "),n("el-col",{attrs:{span:6}},[n("div",{staticStyle:{"text-align":"right","padding-right":"10px"}},[n("span",{staticClass:"autoSave",staticStyle:{"margin-right":"10px","font-size":"12px"}},[n("el-switch",{attrs:{"active-text":"自动保存"},on:{change:e.autoSaveSurvey},model:{value:e.isAutoSave,callback:function(t){e.isAutoSave=t},expression:"isAutoSave"}}),e._v(" "),n("span",{staticStyle:{color:"#afafb0"}},[e._v("("+e._s(e.autoSaveTime)+")")])],1),e._v(" "),n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.devSurvey}},[n("i",{staticClass:"fa fa-paper-plane"}),e._v(" 发布")]),e._v(" "),n("el-button",{attrs:{plain:"",size:"mini"},on:{click:e.saveSurvey}},[n("i",{staticClass:"fa-solid fa-floppy-disk"}),e._v(" 保存")])],1)])],1)],1)},s=[]},"zt+G":function(e,t,n){"use strict";n.r(t);var r=n("nNYl"),s=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,function(){return r[e]})}(a);var i=n("WEnS"),o=n("JFUb"),u=Object(o.a)(s.a,i.a,i.b,!1,null,null,null);t.default=u.exports}}]);