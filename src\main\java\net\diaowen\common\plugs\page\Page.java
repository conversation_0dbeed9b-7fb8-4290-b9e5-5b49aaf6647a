package net.diaowen.common.plugs.page;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Page<T> implements Serializable {
    private int pageNo = 1;
    private int pageSize = 10;
    private int total;
    private List<T> list;

    // 兼容性字段
    private long totalItems;
    private String orderBy;
    private String orderDir;
    private String scrollId;
    private boolean countTotal = true;
    private boolean islastpage = false;
    private int totalPage;

    public Page() {
    }

    public Page(PageRequest pageRequest) {
        if (pageRequest != null) {
            this.pageNo = pageRequest.getPageNo();
            this.pageSize = pageRequest.getPageSize();
            this.orderBy = pageRequest.getOrderBy();
            this.orderDir = pageRequest.getOrderDir();
            this.countTotal = pageRequest.isCountTotal();
            this.islastpage = pageRequest.isIslastpage();
        }
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
        this.totalItems = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    // 兼容性方法
    public List<T> getResult() {
        return list;
    }

    public void setResult(List<T> result) {
        this.list = result;
    }

    public void addResult(T item) {
        if (this.list == null) {
            this.list = new ArrayList<>();
        }
        this.list.add(item);
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
        this.total = (int) totalItems;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderDir() {
        return orderDir;
    }

    public void setOrderDir(String orderDir) {
        this.orderDir = orderDir;
    }

    public String getScrollId() {
        return scrollId;
    }

    public void setScrollId(String scrollId) {
        this.scrollId = scrollId;
    }

    public boolean isCountTotal() {
        return countTotal;
    }

    public void setCountTotal(boolean countTotal) {
        this.countTotal = countTotal;
    }

    public boolean isIslastpage() {
        return islastpage;
    }

    public void setIslastpage(boolean islastpage) {
        this.islastpage = islastpage;
    }

    public int getTotalPage() {
        if (pageSize > 0) {
            return (int) Math.ceil((double) totalItems / pageSize);
        }
        return 0;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }
}