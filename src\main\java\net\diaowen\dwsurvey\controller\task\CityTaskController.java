package net.diaowen.dwsurvey.controller.task;

import net.diaowen.common.base.controller.BaseController;
import net.diaowen.common.base.entity.Result;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.service.CityTaskManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市体检任务控制器
 * 负责处理城市体检任务的增删改查操作
 */
@Controller
@RequestMapping("/api/city-task")
public class CityTaskController extends BaseController {

    @Autowired
    private CityTaskManager cityTaskManager;

    /**
     * 分页查询城市体检任务列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public Result listTasks(HttpServletRequest request) {
        try {
            Page<CityTask> taskPage = new Page<>();
            String curPageNum = request.getParameter("page");
            String pageSizeStr = request.getParameter("pageSize");
            String keyword = request.getParameter("keyword");
            String userId = getUserId(request); // Assuming search is user-specific

            int pageSize = pageSizeStr != null ? Integer.parseInt(pageSizeStr) : 10;
            taskPage.setPageSize(pageSize);
            taskPage.setPageNo(curPageNum != null ? Integer.parseInt(curPageNum) : 1);

            cityTaskManager.searchTasks(taskPage, userId, keyword);

            Map<String, Object> result = new HashMap<>();
            result.put("tasks", taskPage.getResult());
            result.put("total", taskPage.getTotalItems());
            result.put("page", taskPage.getPageNo());
            result.put("pageSize", taskPage.getPageSize());

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    @RequestMapping("/detail/{id}")
    @ResponseBody
    public Result getTaskDetail(@PathVariable String id, HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            CityTask task = cityTaskManager.getTaskDetail(id, userId);
            if (task == null) {
                return Result.error("任务不存在或无权访问");
            }
            return Result.success(task);
        } catch (Exception e) {
            return Result.error("获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建新任务
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public Result createTask(@RequestBody CityTask task, HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            CityTask createdTask = cityTaskManager.createTask(task, userId);
            return Result.success("任务创建成功", createdTask);
        } catch (Exception e) {
            return Result.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务信息
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public Result updateTask(@RequestBody CityTask task, HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            CityTask updatedTask = cityTaskManager.updateTask(task, userId);
            return Result.success("任务更新成功", updatedTask);
        } catch (Exception e) {
            return Result.error("更新任务失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public Result deleteTask(@PathVariable String id, HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            boolean success = cityTaskManager.deleteTask(id, userId);
            if (success) {
                return Result.success("任务删除成功");
            } else {
                return Result.error("任务删除失败或任务不存在");
            }
        } catch (Exception e) {
            return Result.error("删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 分配任务
     */
    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    @ResponseBody
    public Result assignTask(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            String taskId = params.get("taskId");
            String gridId = params.get("gridId");
            String assigneeId = params.get("assigneeId");
            String userId = getUserId(request);

            if (taskId == null || gridId == null || assigneeId == null) {
                return Result.error("参数不完整");
            }

            boolean success = cityTaskManager.assignTask(taskId, gridId, assigneeId, userId);
            if (success) {
                return Result.success("任务分配成功");
            } else {
                return Result.error("任务分配失败");
            }
        } catch (Exception e) {
            return Result.error("分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新任务状态
     */
    @RequestMapping(value = "/batch-update-status", method = RequestMethod.POST)
    @ResponseBody
    public Result batchUpdateStatus(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            List<String> taskIds = (List<String>) params.get("taskIds");
            Integer status = (Integer) params.get("status");
            String userId = getUserId(request);

            if (taskIds == null || taskIds.isEmpty() || status == null) {
                return Result.error("参数不完整");
            }

            int updatedCount = cityTaskManager.batchUpdateStatus(taskIds, status, userId);
            return Result.success("成功更新 " + updatedCount + " 个任务状态");
        } catch (Exception e) {
            return Result.error("批量更新状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @RequestMapping("/statistics")
    @ResponseBody
    public Result getTaskStatistics(HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            Map<String, Object> statistics = cityTaskManager.getTaskStatsByUser(userId);
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据网格ID查询任务
     */
    @RequestMapping("/by-grid/{gridCode}")
    @ResponseBody
    public Result getTasksByGrid(@PathVariable String gridCode, @RequestParam(required = false) Integer status) {
        try {
            List<CityTask> tasks = cityTaskManager.findTasksByGrid(gridCode, status);
            return Result.success(tasks);
        } catch (Exception e) {
            return Result.error("查询网格任务失败: " + e.getMessage());
        }
    }

    /**
     * 我的任务列表
     */
    @RequestMapping("/my-tasks")
    @ResponseBody
    public Result getMyTasks(HttpServletRequest request) {
        try {
            String userId = getUserId(request);
            Page<CityTask> taskPage = new Page<>();
            String curPageNum = request.getParameter("page");
            String pageSizeStr = request.getParameter("pageSize");
            String statusStr = request.getParameter("status");
            String gridCode = request.getParameter("gridCode");

            Integer status = statusStr != null ? Integer.parseInt(statusStr) : null;
            int pageSize = pageSizeStr != null ? Integer.parseInt(pageSizeStr) : 10;
            taskPage.setPageSize(pageSize);
            taskPage.setPageNo(curPageNum != null ? Integer.parseInt(curPageNum) : 1);

            // Note: The service layer expects gridCode, not userId, for my-tasks filtering.
            // This might need alignment with business requirements. Assuming gridCode is passed.
            cityTaskManager.findMyTasks(taskPage, gridCode, status);

            Map<String, Object> result = new HashMap<>();
            result.put("tasks", taskPage.getResult());
            result.put("total", taskPage.getTotalItems());
            result.put("page", taskPage.getPageNo());
            result.put("pageSize", taskPage.getPageSize());

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询我的任务失败: " + e.getMessage());
        }
    }

    /**
     * 提交任务结果
     */
    @RequestMapping(value = "/complete", method = RequestMethod.POST)
    @ResponseBody
    public Result completeTask(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            String taskId = params.get("taskId");
            String formData = params.get("formData");
            String attachments = params.get("attachments");
            String userId = getUserId(request);

            if (taskId == null) {
                return Result.error("任务ID不能为空");
            }

            boolean success = cityTaskManager.completeTask(taskId, formData, attachments, userId);
            if (success) {
                return Result.success("任务完成提交成功");
            } else {
                return Result.error("任务提交失败，请检查任务状态或权限");
            }
        } catch (Exception e) {
            return Result.error("提交任务结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getUserId(HttpServletRequest request) {
        // 从session或token中获取用户ID
        Object userId = request.getSession().getAttribute("userId");
        return userId != null ? userId.toString() : "system-user"; // Default user for testing
    }
}