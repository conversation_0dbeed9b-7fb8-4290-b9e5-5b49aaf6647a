package net.diaowen.common.dao;

import java.io.Serializable;
import java.util.List;

import org.hibernate.criterion.Criterion;

import net.diaowen.common.plugs.page.Page;


/**
 * <AUTHOR>
 *
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface BaseDao<T,ID extends Serializable> extends IHibernateDao<T, ID>{

	Page<T> findByCondition(Page<T> page, List<Criterion> criterions);

}
