(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{"+1tm":function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.dwCheckValue=function(e,r,t){var n={isOK:!0,msg:""};if((null===r||void 0===r||r.length<=0)&&(n.isOK=!1,n.msg="输入值不能为空"),"NO"===e)r.length<=0&&(n.isOK=!1,n.msg="输入值不能为空");else if("EMAIL"===e)/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(r)||(n.isOK=!1,n.msg="请输入值邮箱地址");else if("UNSTRCN"===e)/^[\u3220-\uFA29]+$/.test(r)&&(n.isOK=!1,n.msg="请输入非中文字符");else if("STRCN"===e)/^[\u3220-\uFA29]+$/.test(r)||(n.isOK=!1,n.msg="请输入中文字符");else if("NUM"===e)/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(r)||(n.isOK=!1,n.msg="请输入数字");else if("DIGITS"===e)/^\d+$/.test(r)||(n.isOK=!1,n.msg="请输入整数");else if("TEL"===e)/^\d{3,4}-{1}\d{7,9}-?\d{1,6}$/.test(r)||/^(400{1}-?[0-9]{3}-?[0-9]{4})|(800{1}-?[0-9]{3}-?[0-9]{4})$/.test(r)||(n.isOK=!1,n.msg="请输入电话号");else if("PHONE"===e)/^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(r)||(n.isOK=!1,n.msg="请输入手机号");else if("TEL_PHONE"===e)/^(400{1}-?[0-9]{3}-?[0-9]{4})|(800{1}-?[0-9]{3}-?[0-9]{4})$/.test(r)||/^\d{3,4}-{1}\d{7,9}-?\d{1,6}$/.test(r)||/^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(r)||(n.isOK=!1,n.msg="请输入电话或手机号");else if("DATE"===e){var o=new RegExp(/^[1-9]\d{3}$/),a=new RegExp(/^[1-9]\d{3}-(0[1-9]|1[0-2])$/),u=new RegExp(/^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/),s=new RegExp(/^(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/),i=new RegExp(/^(20|21|22|23|[0-1]\d):[0-5]\d$/),l=new RegExp(/^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/);o.test(r)||u.test(r)||a.test(r)||s.test(r)||l.test(r)||i.test(r)||(n.isOK=!1,n.msg="请输入日期，如2014-01-01")}else"IDENT_CODE"===e?(!/^\d{15}|(\d{17}(\d|x|X))$/.test(r)||15!==r.length&&18!==r.length)&&(n.isOK=!1,n.msg="请输入身份证号"):"ZIPCODE"===e?/^[0-9]{6}$/.test(r)||(n.isOK=!1,n.msg="请输入邮政编号"):"URL"===e?/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(r)||(n.isOK=!1,n.msg="请输入网址URL"):"CUSTOM"===e&&(new RegExp(t).test(r)||(n.isOK=!1,n.msg="不符合校验规则"));return n},r.secondsToHms=function(e){var r=Math.floor(e/3600),t=Math.floor(e%3600/60),n=e%60;return r>0?(r<10?"0"+r:r)+" 时 "+(t<10?"0"+t:t)+" 分 "+(n<10?"0"+n:n)+" 秒":t>0?(t<10?"0"+t:t)+" 分 "+(n<10?"0"+n:n)+" 秒":(n<10?"0"+n:n)+" 秒"}},"0tVQ":function(e,r,t){t("FlQf"),t("VJsP"),e.exports=t("WEpk").Array.from},"1y4o":function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n,o=(n=t("gDS+"))&&n.__esModule?n:{default:n};r.parseAnswerData=function(e,r){if(e.dwEsSurveyAnswer=r,e.firstLoadAnswer=!0,null!=r&&r.hasOwnProperty("anQuestions")){var t=e.questions,n=r.anQuestions;null!==n&&n.length>0&&n.forEach(function(e,r){var n=e.quDwId;t.forEach(function(r,t){var o=r.dwId;n===o&&(i(r,e),console.debug("surveyQuId",o))})})}console.debug("parseAnswerData survey",e)},r.parseAnQuAnswer2SurveyQu=i,r.initAnswerBySurvey=function(e){var r=e.questions;if(e.surveyAttrs.hasOwnProperty("opoqAttr")&&e.surveyAttrs.opoqAttr.enabled){var t=(0,o.default)({quType:"PAGETAG",tempPage:!0,quTitleObj:{dwHtml:"",dwText:"",dwPlaceholder:"请输入题目标题",isNew:!1},quNoteObj:{dwHtml:"",dwText:"",dwPlaceholder:"请输入题目备注",isNew:!1}});r.forEach(function(e,n){var o=JSON.parse(t),a=null;r.length>n+1&&(a=r[n+1]),"PAGETAG"!==e.quType&&null!==a&&"PAGETAG"!==a.quType&&r.splice(n+1,0,o)})}if((0,s.parseQuestions)(r,!0),null!==r&&r.length>0){var n=1;r.forEach(function(e,r){var t=e.quType,o=e.quAttr;if("FILLBLANK"===t){var a=o.inputAttr;!function(e){var r=e.commonAttr,t=r.checkType,n=r.placeholder,o="请输入";void 0!==n&&null!==n&&""!==n&&"请输入"!==n||("EMAIL"===t?o="请输入邮箱":"NUM"===t?o="请输入数字":"DIGITS"===t?o="请输入整数":"TEL_PHONE"===t?o="请选择电话或手机":"TIME"===t?o="请选择时间":"DATE"===t?o="请选择日期":"DATETIME"===t?o="请选择日期时间":"ZIPCODE"===t?o="请输入邮政编码":"TEL"===t?o="请输入电话号码":"PHONE"===t?o="请输入手机号码":"IDENTCODE"===t?o="请输入身份证号":"URL"===t?o="请输入网址":"UNSTRCN"===t?o="请输入非中文":"STRCN"===t&&(o="请输入中文"),r.placeholder=o)}(a);var s=a.commonAttr;"TIME"===s.checkType&&a.dateTimeAttr.attrs.includes("range")&&(e.answer={startTime:null,endTime:null});var i=s.defaultValue;null!==i&&""!==i&&(e.answer=i),(0,u.getQuestionAnswerData)(e)}else if("MULTIFILLBLANK"===t)console.debug("answer MULTIFILLBLANK");else if("RADIO"===t)console.debug("answer RADIO");else if("CHECKBOX"===t)console.debug("answer CHECKBOX");else if("MATRIX_RADIO"===t||"MATRIX_CHECKBOX"===t||"MATRIX_INPUT"===t)c(e);else if("MATRIX_SCALE"===t||"MATRIX_SLIDER"===t){e.quRows.forEach(function(e,r){e.answerValue=null,e.sliderAnswerValue=null})}e.showQu=!0,e.logicIsHide=!1,e.pageIndex=n,"PAGETAG"===e.quType&&n++})}e.pageAttr={pageSize:a.surveyPageUtils.pageSize(e),curPage:1,begin:null,end:null},e.answerProgress={totalAnQu:0,completeAnQu:0,percentage:0}},r.buildMatrixQuRowCols=c,r.showPageByIndex=function e(r,t,n){var o=null,a=null,u=!1,s=!1;r.questions.forEach(function(e,r){(console.debug("item.pageIndex === pageIndex",e.pageIndex===t),e.showQu=e.pageIndex===t,e.showQu&&(null==o&&(o=r),a=r),e.pageIndex===t)&&(e.logicIsHide||(u=!0),"PAGETAG"===e.quType&&(u?e.logicIsHide=!1:(e.logicIsHide=!0,s=!0)))}),r.pageAttr.curPage=t,r.pageAttr.begin=o,r.pageAttr.end=a,s&&e(r,"next"===n?t+1:t-1,n)};var a=t("heCB"),u=t("BNG+"),s=(t("C8a3"),t("VeDC"));function i(e,r){var t=e.quType;console.debug("parseAnQuAnswer2SurveyQu",t),"RADIO"===t?function(e,r){var t=e.quRadios;if(r.hasOwnProperty("anRadio")){var n=r.anRadio;if(null!=n&&n.hasOwnProperty("optionDwId")){var o=n.optionDwId;void 0!==o&&null!==o&&t.map(function(e,r){o===e.dwId&&(e.checked=!0,e.otherText=n.otherText)}),e.anQuestion=r}}}(e,r):"CHECKBOX"===t?function(e,r){var t=e.quCheckboxs;if(r.hasOwnProperty("anCheckboxs")){var n=r.anCheckboxs;null!==n&&n.length>0&&(n.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")){var n=e.optionDwId;t.forEach(function(r,t){r.dwId===n&&(r.checked=!0,r.otherText=e.otherText)})}}),e.anQuestion=r)}}(e,r):"ORDERQU"===t?function(e,r){var t=e.quOrderbys;if(r.hasOwnProperty("anOrders")){var n=r.anOrders;null!==n&&n.length>0&&(n.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")){var n=e.optionDwId;t.forEach(function(r,t){r.dwId===n&&(r.checked=!0,r.orderIndex=e.orderNum)})}}),e.anQuestion=r)}}(e,r):"MULTIFILLBLANK"===t?function(e,r){var t=e.quMultiFillblanks;if(r.hasOwnProperty("anMFbks")){var n=r.anMFbks;null!==n&&n.length>0&&(n.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")){var n=e.optionDwId;t.forEach(function(r,t){r.dwId===n&&(r.inputText=e.answer)})}}),e.anQuestion=r)}}(e,r):"SCORE"===t?function(e,r){var t=e.quScores;if(r.hasOwnProperty("anScores")){var n=r.anScores;null!==n&&n.length>0&&(n.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")){var n=e.optionDwId;t.forEach(function(r,t){r.dwId===n&&(r.checked=!0,null!==e.answerScore&&e.answerScore>=0&&(r.answerScore=parseInt(e.answerScore)))})}}),e.anQuestion=r)}}(e,r):"FILLBLANK"===t?function(e,r){if(r.hasOwnProperty("anFbk")){var t=r.anFbk;null!==t&&(e.answer=t.answer,e.anQuestion=r)}}(e,r):"UPLOADFILE"===t?function(e,r){if(r.hasOwnProperty("anUploadFiles")){var t=r.anUploadFiles;if(null!==t){var n=[];t.forEach(function(e,r){n.push({response:{data:[{location:e.filePath,filename:e.fileName}]},name:e.fileName})}),e.upFileList=n,e.anQuestion=r}}}(e,r):"MATRIX_RADIO"===t?function(e,r){var t=e.quRows,n=r.anMatrixRadios;null!=n&&(n.forEach(function(e,r){var n=e.rowDwId,o=e.colDwId;t.forEach(function(e,r){var t=e.dwId;e.rowCols.forEach(function(e,r){var a=e.dwId;if(n===t&&o===a)return e.checked=!0,!0})})}),e.anQuestion=r)}(e,r):"MATRIX_CHECKBOX"===t?function(e,r){var t=e.quRows,n=r.anMatrixCheckboxes;null!=n&&(t.forEach(function(e,r){var t=e.dwId;e.rowCols.forEach(function(e,r){var o=e.dwId;n.forEach(function(r,n){var a=r.rowDwId;r.rowAnCheckboxs.forEach(function(r,n){var u=r.optionDwId;if(a===t&&u===o)return e.checked=!0,!0})})})}),e.anQuestion=r)}(e,r):"MATRIX_INPUT"===t?function(e,r){var t=e.quRows,n=r.anMatrixFbks;null!=n&&(t.forEach(function(e,r){var t=e.dwId;e.rowCols.forEach(function(e,r){var o=e.dwId;n.forEach(function(r,n){var a=r.rowDwId;r.rowAnFbks.forEach(function(r,n){var u=r.optionDwId;if(a===t&&u===o)return e.answerValue=r.answer,!0})})})}),e.anQuestion=r)}(e,r):"MATRIX_SCALE"===t?l(e,r):"MATRIX_SLIDER"===t&&l(e,r)}function l(e,r){var t=e.quRows,n=r.anMatrixScales;null!=n&&(t.forEach(function(e,r){var t=e.dwId;n.forEach(function(r,n){var o=r.rowDwId,a=r.answerScore;if(o===t&&null!==a&&void 0!==a)return e.answerValue=parseFloat(a),e.sliderAnswerValue=parseFloat(a),!0})}),e.anQuestion=r)}function c(e){var r=e.quRows,t=e.quCols;null!==r&&void 0!==r&&r.length>0&&null!==t&&void 0!==t&&t.length>0&&r.forEach(function(e,r){var n=[];t.forEach(function(e){n.push({dwId:e.dwId,checked:!1,answerValue:null,tempEmptyOption:e.tempEmptyOption})}),e.rowCols=n})}},"4USb":function(e,r,t){"use strict";let n;t.r(r),t.d(r,"v1",function(){return v}),t.d(r,"v3",function(){return b}),t.d(r,"v4",function(){return E}),t.d(r,"v5",function(){return x}),t.d(r,"NIL",function(){return N}),t.d(r,"version",function(){return D}),t.d(r,"validate",function(){return s}),t.d(r,"stringify",function(){return c}),t.d(r,"parse",function(){return y});const o=new Uint8Array(16);function a(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(o)}var u=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,s=function(e){return"string"==typeof e&&u.test(e)};const i=[];for(let e=0;e<256;++e)i.push((e+256).toString(16).slice(1));function l(e,r=0){return i[e[r+0]]+i[e[r+1]]+i[e[r+2]]+i[e[r+3]]+"-"+i[e[r+4]]+i[e[r+5]]+"-"+i[e[r+6]]+i[e[r+7]]+"-"+i[e[r+8]]+i[e[r+9]]+"-"+i[e[r+10]]+i[e[r+11]]+i[e[r+12]]+i[e[r+13]]+i[e[r+14]]+i[e[r+15]]}var c=function(e,r=0){const t=l(e,r);if(!s(t))throw TypeError("Stringified UUID is invalid");return t};let d,p,f=0,w=0;var v=function(e,r,t){let n=r&&t||0;const o=r||new Array(16);let u=(e=e||{}).node||d,s=void 0!==e.clockseq?e.clockseq:p;if(null==u||null==s){const r=e.random||(e.rng||a)();null==u&&(u=d=[1|r[0],r[1],r[2],r[3],r[4],r[5]]),null==s&&(s=p=16383&(r[6]<<8|r[7]))}let i=void 0!==e.msecs?e.msecs:Date.now(),c=void 0!==e.nsecs?e.nsecs:w+1;const v=i-f+(c-w)/1e4;if(v<0&&void 0===e.clockseq&&(s=s+1&16383),(v<0||i>f)&&void 0===e.nsecs&&(c=0),c>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");f=i,w=c,p=s;const y=(1e4*(268435455&(i+=122192928e5))+c)%4294967296;o[n++]=y>>>24&255,o[n++]=y>>>16&255,o[n++]=y>>>8&255,o[n++]=255&y;const h=i/4294967296*1e4&268435455;o[n++]=h>>>8&255,o[n++]=255&h,o[n++]=h>>>24&15|16,o[n++]=h>>>16&255,o[n++]=s>>>8|128,o[n++]=255&s;for(let e=0;e<6;++e)o[n+e]=u[e];return r||l(o)},y=function(e){if(!s(e))throw TypeError("Invalid UUID");let r;const t=new Uint8Array(16);return t[0]=(r=parseInt(e.slice(0,8),16))>>>24,t[1]=r>>>16&255,t[2]=r>>>8&255,t[3]=255&r,t[4]=(r=parseInt(e.slice(9,13),16))>>>8,t[5]=255&r,t[6]=(r=parseInt(e.slice(14,18),16))>>>8,t[7]=255&r,t[8]=(r=parseInt(e.slice(19,23),16))>>>8,t[9]=255&r,t[10]=(r=parseInt(e.slice(24,36),16))/1099511627776&255,t[11]=r/4294967296&255,t[12]=r>>>24&255,t[13]=r>>>16&255,t[14]=r>>>8&255,t[15]=255&r,t};const h="6ba7b810-9dad-11d1-80b4-00c04fd430c8",A="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function O(e,r,t){function n(e,n,o,a){var u;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));const r=[];for(let t=0;t<e.length;++t)r.push(e.charCodeAt(t));return r}(e)),"string"==typeof n&&(n=y(n)),16!==(null===(u=n)||void 0===u?void 0:u.length))throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let s=new Uint8Array(16+e.length);if(s.set(n),s.set(e,n.length),(s=t(s))[6]=15&s[6]|r,s[8]=63&s[8]|128,o){a=a||0;for(let e=0;e<16;++e)o[a+e]=s[e];return o}return l(s)}try{n.name=e}catch(e){}return n.DNS=h,n.URL=A,n}function g(e){return 14+(e+64>>>9<<4)+1}function I(e,r){const t=(65535&e)+(65535&r);return(e>>16)+(r>>16)+(t>>16)<<16|65535&t}function m(e,r,t,n,o,a){return I((u=I(I(r,e),I(n,a)))<<(s=o)|u>>>32-s,t);var u,s}function T(e,r,t,n,o,a,u){return m(r&t|~r&n,e,r,o,a,u)}function S(e,r,t,n,o,a,u){return m(r&n|t&~n,e,r,o,a,u)}function P(e,r,t,n,o,a,u){return m(r^t^n,e,r,o,a,u)}function q(e,r,t,n,o,a,u){return m(t^(r|~n),e,r,o,a,u)}var b=O("v3",48,function(e){if("string"==typeof e){const r=unescape(encodeURIComponent(e));e=new Uint8Array(r.length);for(let t=0;t<r.length;++t)e[t]=r.charCodeAt(t)}return function(e){const r=[],t=32*e.length;for(let n=0;n<t;n+=8){const t=e[n>>5]>>>n%32&255,o=parseInt("0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t),16);r.push(o)}return r}(function(e,r){e[r>>5]|=128<<r%32,e[g(r)-1]=r;let t=1732584193,n=-271733879,o=-1732584194,a=271733878;for(let r=0;r<e.length;r+=16){const u=t,s=n,i=o,l=a;n=q(n=q(n=q(n=q(n=P(n=P(n=P(n=P(n=S(n=S(n=S(n=S(n=T(n=T(n=T(n=T(n,o=T(o,a=T(a,t=T(t,n,o,a,e[r],7,-680876936),n,o,e[r+1],12,-389564586),t,n,e[r+2],17,606105819),a,t,e[r+3],22,-1044525330),o=T(o,a=T(a,t=T(t,n,o,a,e[r+4],7,-176418897),n,o,e[r+5],12,1200080426),t,n,e[r+6],17,-1473231341),a,t,e[r+7],22,-45705983),o=T(o,a=T(a,t=T(t,n,o,a,e[r+8],7,1770035416),n,o,e[r+9],12,-1958414417),t,n,e[r+10],17,-42063),a,t,e[r+11],22,-1990404162),o=T(o,a=T(a,t=T(t,n,o,a,e[r+12],7,1804603682),n,o,e[r+13],12,-40341101),t,n,e[r+14],17,-1502002290),a,t,e[r+15],22,1236535329),o=S(o,a=S(a,t=S(t,n,o,a,e[r+1],5,-165796510),n,o,e[r+6],9,-1069501632),t,n,e[r+11],14,643717713),a,t,e[r],20,-373897302),o=S(o,a=S(a,t=S(t,n,o,a,e[r+5],5,-701558691),n,o,e[r+10],9,38016083),t,n,e[r+15],14,-660478335),a,t,e[r+4],20,-405537848),o=S(o,a=S(a,t=S(t,n,o,a,e[r+9],5,568446438),n,o,e[r+14],9,-1019803690),t,n,e[r+3],14,-187363961),a,t,e[r+8],20,1163531501),o=S(o,a=S(a,t=S(t,n,o,a,e[r+13],5,-1444681467),n,o,e[r+2],9,-51403784),t,n,e[r+7],14,1735328473),a,t,e[r+12],20,-1926607734),o=P(o,a=P(a,t=P(t,n,o,a,e[r+5],4,-378558),n,o,e[r+8],11,-2022574463),t,n,e[r+11],16,1839030562),a,t,e[r+14],23,-35309556),o=P(o,a=P(a,t=P(t,n,o,a,e[r+1],4,-1530992060),n,o,e[r+4],11,1272893353),t,n,e[r+7],16,-155497632),a,t,e[r+10],23,-1094730640),o=P(o,a=P(a,t=P(t,n,o,a,e[r+13],4,681279174),n,o,e[r],11,-358537222),t,n,e[r+3],16,-722521979),a,t,e[r+6],23,76029189),o=P(o,a=P(a,t=P(t,n,o,a,e[r+9],4,-640364487),n,o,e[r+12],11,-421815835),t,n,e[r+15],16,530742520),a,t,e[r+2],23,-995338651),o=q(o,a=q(a,t=q(t,n,o,a,e[r],6,-198630844),n,o,e[r+7],10,1126891415),t,n,e[r+14],15,-1416354905),a,t,e[r+5],21,-57434055),o=q(o,a=q(a,t=q(t,n,o,a,e[r+12],6,1700485571),n,o,e[r+3],10,-1894986606),t,n,e[r+10],15,-1051523),a,t,e[r+1],21,-2054922799),o=q(o,a=q(a,t=q(t,n,o,a,e[r+8],6,1873313359),n,o,e[r+15],10,-30611744),t,n,e[r+6],15,-1560198380),a,t,e[r+13],21,1309151649),o=q(o,a=q(a,t=q(t,n,o,a,e[r+4],6,-145523070),n,o,e[r+11],10,-1120210379),t,n,e[r+2],15,718787259),a,t,e[r+9],21,-343485551),t=I(t,u),n=I(n,s),o=I(o,i),a=I(a,l)}return[t,n,o,a]}(function(e){if(0===e.length)return[];const r=8*e.length,t=new Uint32Array(g(r));for(let n=0;n<r;n+=8)t[n>>5]|=(255&e[n/8])<<n%32;return t}(e),8*e.length))}),R={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},E=function(e,r,t){if(R.randomUUID&&!r&&!e)return R.randomUUID();const n=(e=e||{}).random||(e.rng||a)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,r){t=t||0;for(let e=0;e<16;++e)r[t+e]=n[e];return r}return l(n)};function L(e,r,t,n){switch(e){case 0:return r&t^~r&n;case 1:return r^t^n;case 2:return r&t^r&n^t&n;case 3:return r^t^n}}function C(e,r){return e<<r|e>>>32-r}var x=O("v5",80,function(e){const r=[1518500249,1859775393,2400959708,3395469782],t=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){const r=unescape(encodeURIComponent(e));e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const n=e.length/4+2,o=Math.ceil(n/16),a=new Array(o);for(let r=0;r<o;++r){const t=new Uint32Array(16);for(let n=0;n<16;++n)t[n]=e[64*r+4*n]<<24|e[64*r+4*n+1]<<16|e[64*r+4*n+2]<<8|e[64*r+4*n+3];a[r]=t}a[o-1][14]=8*(e.length-1)/Math.pow(2,32),a[o-1][14]=Math.floor(a[o-1][14]),a[o-1][15]=8*(e.length-1)&4294967295;for(let e=0;e<o;++e){const n=new Uint32Array(80);for(let r=0;r<16;++r)n[r]=a[e][r];for(let e=16;e<80;++e)n[e]=C(n[e-3]^n[e-8]^n[e-14]^n[e-16],1);let o=t[0],u=t[1],s=t[2],i=t[3],l=t[4];for(let e=0;e<80;++e){const t=Math.floor(e/20),a=C(o,5)+L(t,u,s,i)+l+r[t]+n[e]>>>0;l=i,i=s,s=C(u,30)>>>0,u=o,o=a}t[0]=t[0]+o>>>0,t[1]=t[1]+u>>>0,t[2]=t[2]+s>>>0,t[3]=t[3]+i>>>0,t[4]=t[4]+l>>>0}return[t[0]>>24&255,t[0]>>16&255,t[0]>>8&255,255&t[0],t[1]>>24&255,t[1]>>16&255,t[1]>>8&255,255&t[1],t[2]>>24&255,t[2]>>16&255,t[2]>>8&255,255&t[2],t[3]>>24&255,t[3]>>16&255,t[3]>>8&255,255&t[3],t[4]>>24&255,t[4]>>16&255,t[4]>>8&255,255&t[4]]}),N="00000000-0000-0000-0000-000000000000",D=function(e){if(!s(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)}},"BNG+":function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.getSurveyAnswerData=function(e){var r=e.dwId,t=(0,n.v4)(),a={answerCommon:{surveyId:e.id,sid:e.sid,surveyDwId:r,answerDwId:t,anUser:{userId:null,userName:null},anTime:{bgAnDate:null,endAnDate:null,totalTime:0},anIp:{ip:null,city:null,addr:null},anState:{anQuNum:0,isEff:1,handleState:0},isDelete:0},anQuestions:[]},u=e.questions,s=0;return void 0!==u&&(a.anQuestions=u.map(function(e,r){var t=o(e);return e.hasOwnProperty("item")&&e.isAn&&s++,t})),a.answerCommon.anState.anQuNum=s,a},r.getQuestionAnswerData=o;var n=t("4USb");function o(e){var r=e.quType,t={quDwId:e.dwId,quType:r};return e.isAn=!1,"RADIO"===r?function(e,r){e.quRadios.map(function(t,n){t.hasOwnProperty("checked")&&t.checked&&(r.anRadio={optionDwId:t.dwId,otherText:t.otherText},e.isAn=!0)})}(e,t):"CHECKBOX"===r?function(e,r){var t=e.quCheckboxs;r.anCheckboxs=[],t.map(function(t,n){t.hasOwnProperty("checked")&&t.checked&&(r.anCheckboxs.push({optionDwId:t.dwId,otherText:t.otherText}),e.isAn=!0)})}(e,t):"ORDERQU"===r?function(e,r){console.debug("getQuOrderByAnswerData question",e),console.debug("anQuestion",r);var t=e.quOrderbys;r.anOrders=[],t.forEach(function(t,n){t.hasOwnProperty("checked")&&t.checked&&t.orderIndex>0&&(r.anOrders.push({optionDwId:t.dwId,orderNum:t.orderIndex}),e.isAn=!0)})}(e,t):"MULTIFILLBLANK"===r?function(e,r){var t=e.quMultiFillblanks;r.anMFbks=[],t.map(function(t,n){t.hasOwnProperty("inputText")&&""!==t.inputText&&(r.anMFbks.push({optionDwId:t.dwId,answer:t.inputText}),e.isAn=!0)})}(e,t):"SCORE"===r?function(e,r){var t=e.quScores;r.anScores=[],t.map(function(t,n){t.hasOwnProperty("checked")&&t.checked&&t.hasOwnProperty("answerScore")&&(r.anScores.push({optionDwId:t.dwId,answerScore:t.answerScore}),e.isAn=!0)})}(e,t):"FILLBLANK"===r?function(e,r){t.anFbk={answer:e.answer},e.isAn=!0}(e):"UPLOADFILE"===r?function(e,r){if(r.anUploadFiles=[],e.hasOwnProperty("upFileList")){var t=e.upFileList;void 0!==t&&null!==t&&t.forEach(function(t,n){t.hasOwnProperty("response")&&t.response.hasOwnProperty("data")&&null!=t.response.data&&t.response.data.forEach(function(t){var n={filePath:t.location,fileName:t.filename};r.anUploadFiles.push(n),e.isAn=!0})})}}(e,t):"MATRIX_RADIO"===r?function(e,r){var t=[];e.quRows.map(function(r,n){r.rowCols.map(function(n,o){n.hasOwnProperty("checked")&&n.checked&&(t.push({rowDwId:r.dwId,colDwId:n.dwId,quAnScore:0}),e.isAn=!0)})}),r.anMatrixRadios=t}(e,t):"MATRIX_CHECKBOX"===r?function(e,r){var t=[];e.quRows.map(function(r,n){var o=[];r.rowCols.map(function(r,t){r.hasOwnProperty("checked")&&r.checked&&(o.push({optionDwId:r.dwId,otherText:null}),e.isAn=!0)}),o.length>0&&t.push({rowDwId:r.dwId,rowAnCheckboxs:o,rowAnScore:0})}),r.anMatrixCheckboxes=t}(e,t):"MATRIX_INPUT"===r?function(e,r){var t=[];e.quRows.map(function(r,n){var o=[];r.rowCols.map(function(r,t){null!==r.answerValue&&void 0!==r.answerValue&&r.answerValue.length>0&&(o.push({optionDwId:r.dwId,answer:r.answerValue}),e.isAn=!0)}),t.push({rowDwId:r.dwId,rowAnFbks:o})}),r.anMatrixFbks=t}(e,t):"MATRIX_SCALE"===r?a(e,t):"MATRIX_SLIDER"===r&&a(e,t),e.anQuestion=t,t}function a(e,r){var t=[];e.quRows.map(function(r,n){var o=r.answerValue;null!==o&&void 0!==o&&(t.push({rowDwId:r.dwId,answerScore:o,rowAnScore:0}),e.isAn=!0)}),console.debug("anQuRows",t),r.anMatrixScales=t}},C8a3:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.getSurveyAnswerJsonBySurveyId=function(e,r,t){(0,n.dwSurveyJsonBySurveyId)(e).then(function(e){var n=e.data;if(200===n.resultCode){var a=n.data;if(a.hasOwnProperty("answerCheckResult")&&a.hasOwnProperty("surveyJson")){var u=a.answerCheckResult,s=a.surveyJson;null!==s&&s.hasOwnProperty("surveyJsonText")&&null!==s.surveyJsonText&&""!==s.surveyJsonText?r((0,o.parseSurvey)(JSON.parse(s.surveyJsonText)),u):t(u)}}})},r.answerQuEventCommon=function(e,r){c(e,r,!1)},r.answerQuEventCommonExt=c;var n=t("TdIW"),o=t("VeDC"),a=t("BNG+"),u=t("ZXcj"),s=t("HCiu"),i=t("DZWd"),l=t("pC62");function c(e,r,t){e.questions[r].hasOwnProperty("showOptionError")&&!e.questions[r].showOptionError&&t||e.questions[r].hasOwnProperty("showOptionError"),(0,a.getQuestionAnswerData)(e.questions[r]),(0,l.dwSurveyAnswerLogic)(e,r),(0,u.validateQuestion)(e.questions[r]),s.surveyAnswerLocalStorage.saveSurveyAnswer2LocalStorage(e),(0,i.answerSurveyProgress)(e)}},CG6M:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.questionComps=function(e){return(0,o.default)({url:"/api/dwsurvey/app/v6/dw-design-survey/toolbar-qus.do",method:"get",params:e})},r.querySurveyAll=function(e){return(0,o.default)({url:"/api/dwsurvey/app/design/survey-design/surveyAll.do",method:"post",params:e})},r.surveyJsonDesignBySurveyId=function(e){return(0,o.default)({url:"/api/dwsurvey/app/v6/dw-design-survey/survey-json-by-survey-id.do",method:"get",params:e})},r.dwSaveSurveyJson=function(e){return(0,o.default)({url:"/api/dwsurvey/app/v6/dw-design-survey/save-survey-json.do",method:"post",data:e})},r.dwDevSurvey=function(e){return(0,o.default)({url:"/api/dwsurvey/app/v6/dw-design-survey/dev-survey.do",method:"post",params:e})};var n,o=(n=t("t3Un"))&&n.__esModule?n:{default:n}},DZWd:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.initAnswerSurveyProgress=function(e){e.hasOwnProperty("answerProgress")||o(e,function(e){return(0,n.validateQuestion)(e),e.validateObj.isOk=!0,e.validateObj.isAnswerOk})},r.answerSurveyProgress=function(e){o(e,function(e){return e.validateObj.isAnswerOk})};var n=t("ZXcj");function o(e,r){var t={totalAnQu:0,completeAnQu:0,percentage:0};e.questions.forEach(function(e,n){if(!function(e){var r=e.quType;return"PAGETAG"===r||"PARAGRAPH"===r||"DIVIDER"===r||"CAROUSEL"===r||"MAP"===r}(e)&&e.hasOwnProperty("quAttr")){var o=e.quAttr;o.hasOwnProperty("isRequired")&&o.isRequired&&!e.logicIsHide&&(t.totalAnQu=t.totalAnQu+1,r(e)&&(t.completeAnQu=t.completeAnQu+1))}}),t.totalAnQu>0&&(t.percentage=(t.completeAnQu/t.totalAnQu*100).toFixed(2)),e.answerProgress.totalAnQu=t.totalAnQu,e.answerProgress.completeAnQu=t.completeAnQu,e.answerProgress.percentage=t.percentage}},FyfS:function(e,r,t){e.exports={default:t("Rp86"),__esModule:!0}},HCiu:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.surveyAnswerResultLocalStorage=r.surveyAnswerLocalStorage=r.surveyInitLocalStorage=r.surveyLocalStorageKeyType=void 0;var n,o=(n=t("gDS+"))&&n.__esModule?n:{default:n};r.getEsId=i;var a=t("mGn2"),u=r.surveyLocalStorageKeyType={INIT:"survey_init",AN_HISTORY:"survey_answer_history",AN_HISTORY_ACTION:"survey_answer_history_action"};function s(e){return void 0===e||null===e?"":"_"+e}function i(e){return e.hasOwnProperty("dwEsSurveyAnswer")&&e.dwEsSurveyAnswer.hasOwnProperty("esId")?e.dwEsSurveyAnswer.esId:null}r.surveyInitLocalStorage={saveSurvey2LocalStorage:function(e){if(null!==e){var r=e.sid,t=i(e);this.saveSurvey2LocalStorageByParams(r,t,e)}},saveSurvey2LocalStorageByParams:function(e,r,t){var n=(0,a.buildSurveyLocalStorageKey)(e,""+u.INIT+s(r));(0,a.saveJsonObj2LocalStorage)(n,t)},getSurveyByLocalStorage:function(e,r){var t=(0,a.buildSurveyLocalStorageKey)(e,""+u.INIT+s(r)),n=(0,a.getLocalStorageByKey)(t);return JSON.parse(n)}},r.surveyAnswerLocalStorage={saveSurveyAnswer2LocalStorage:function(e){if(null!==e){var r=e.sid,t=i(e);this.saveSurveyAnswer2LocalStorageByParams(r,t,e)}},saveSurveyAnswer2LocalStorageByParams:function(e,r,t){var n=parseInt(this.getSurveyAnswerActionNum(e,r)),i=this.getSurveyAnswerTextByLocalStorage(e,r);if((0,o.default)(t)!==i){var l=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY+s(r)+"_"+n);(0,a.saveJsonObj2LocalStorage)(l,t),this.saveSurveyAnswerActionNum(e,r,n)}},getSurveyAnswerTextByLocalStorage:function(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;void 0!==t&&null!==t||(t=this.getSurveyAnswerActionNum(e,r));var n=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY+s(r)+"_"+t);return(0,a.getLocalStorageByKey)(n)},getSurveyAnswerObjByLocalStorage:function(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return JSON.parse(this.getSurveyAnswerTextByLocalStorage(e,r,t))},saveSurveyAnswerActionNum:function(e,r,t){var n=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY_ACTION+s(r)),o=(new Date).getTime();return(0,a.saveJsonObj2LocalStorage)(n,{num:0,dateTime:o})},getSurveyAnswerActionNum:function(e,r){var t=this.getSurveyAnswerAction(e,r);return null!==t&&t.hasOwnProperty("num")?t.num:0},getSurveyAnswerActionTime:function(e){if(null!==e){var r=e.sid,t=i(e);return this.getSurveyAnswerActionTimeBySid(r,t)}return 0},getSurveyAnswerActionTimeBySid:function(e,r){var t=this.getSurveyAnswerAction(e,r);return null!==t&&t.hasOwnProperty("dateTime")?t.dateTime:0},getSurveyAnswerAction:function(e,r){var t=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY_ACTION+s(r));if(localStorage.hasOwnProperty(t)){var n=(0,a.getLocalStorageByKey)(t);if(null!==n)return JSON.parse(n)}return null},deleteAnswerHistoryLtNum:function(e,r,t){for(var n=1;n<t;n++){var o=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY+s(r)+"_"+n);localStorage.removeItem(o)}},clearAnswerHistory:function(e,r){for(var t=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY_ACTION+s(r)),n=(0,a.buildSurveyLocalStorageKey)(e,""+u.AN_HISTORY+s(r)),o=localStorage.length,i=[],l=0;l<o;l++){var c=localStorage.key(l);null!=c&&(c.indexOf(t)>=0||c.indexOf(n)>=0)&&i.push(c)}for(var d=0;d<i.length;d++)localStorage.removeItem(i[d])},getSurveyAnswerActionByKey:function(e){if(localStorage.hasOwnProperty(e)){var r=(0,a.getLocalStorageByKey)(e);if(null!==r)return JSON.parse(r)}return null},clearAnswerByDate:function(){var e=this;(0,a.getLocalStorageByKeyword)(u.AN_HISTORY_ACTION).forEach(function(r,t){var n=0,o=e.getSurveyAnswerActionByKey(r);null!==o&&o.hasOwnProperty("dateTime")&&(n=o.dateTime);var u=(new Date).getTime(),s=n+864e5;if(console.debug("lastActionTime",s,u,s<u),s<u){var i=r.replace("survey_answer_history_action","");console.debug("deleteKey sid",i),(0,a.getLocalStorageByKeyword)(i).forEach(function(e,r){localStorage.removeItem(e)})}})}},r.surveyAnswerResultLocalStorage={saveSurvey2LocalStorage:function(e,r,t){var n=(0,a.buildSurveyLocalStorageKey)(e,""+u.INIT+s(r));(0,a.saveJsonObj2LocalStorage)(n,t)},getSurveyByLocalStorage:function(e,r){var t=(0,a.buildSurveyLocalStorageKey)(e,""+u.INIT+s(r)),n=(0,a.getLocalStorageByKey)(t);return JSON.parse(n)}}},IP1Z:function(e,r,t){"use strict";var n=t("2faE"),o=t("rr1i");e.exports=function(e,r,t){r in e?n.f(e,r,o(0,t)):e[r]=t}},KSwC:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n,o=(n=t("gDS+"))&&n.__esModule?n:{default:n};function a(e){var r=e.questions;null!==r&&r.forEach(function(e,t){"PAGETAG"===e.quType&&e.hasOwnProperty("tempPage")&&r.splice(t,1)})}r.logicNum=function(e,r){var t=0;return null!==e&&e.forEach(function(e,n){e.logicType===r&&t++}),t},r.curQuAfterQus=function(e,r){var t=[];if(null!==e){var n=1;e.forEach(function(e,o){"PAGETAG"!==e.quType&&"PARAGRAPH"!==e.quType&&(e.quNum=n++,o>r&&t.push(e))})}return t},r.getQuOptions=function(e){var r=e.quType;return"RADIO"===r?e.quRadios:"CHECKBOX"===r?e.quCheckboxs:"ORDERQU"===r?e.quOrderbys:"MULTIFILLBLANK"===r?e.quMultiFillblanks:"SCORE"===r?e.quScores:void 0},r.clearSurveyJson=function(e){var r=e.questions;null!==r&&r.forEach(function(e,r){})},r.getSurveyJsonSimple=function(e){var r=JSON.parse(e);return r.questions=[],r},r.getSaveSurveyJsonText=function(e){var r=(0,o.default)(e),t=JSON.parse(r);return a(t),t},r.clearSurveyTempPage=a},MBF8:function(e,r,t){"use strict";function n(e,r){null!==r&&r.length>0&&r.forEach(function(e,r){e.checked=!1,e.otherText=null})}function o(e,r){null!==r&&r.length>0&&r.map(function(e,r){e.answerValue=null})}Object.defineProperty(r,"__esModule",{value:!0}),r.clearQuestionAnswer=function(e){var r,t,a={quDwId:e.dwId,quType:u},u=e.quType;"RADIO"===u?n(e,e.quRadios):"CHECKBOX"===u?(n(e,e.quCheckboxs),a.anCheckboxs=[]):"ORDERQU"===u?(null!==(t=e.quOrderbys)&&t.length>0&&t.forEach(function(e,r){e.checked=!1,e.orderIndex=0,e.otherText=null}),a.anOrders=[]):"FILLBLANK"===u?e.answer=null:"MULTIFILLBLANK"===u?(function(e,r){null!==r&&r.length>0&&r.forEach(function(e,r){e.inputText=null})}(0,e.quMultiFillblanks),a.anMFbks=[]):"SCORE"===u?(function(e,r){null!==r&&r.length>0&&r.forEach(function(e,r){e.checked=!1,e.answerScore=0})}(0,e.quScores),a.anScores=[]):"MATRIX_RADIO"===u?(null!==(r=e.quRows)&&r.length>0&&r.map(function(e,r){e.rowCols.map(function(e,r){e.checked=!1})}),a.anMatrixRadios=[]):"MATRIX_CHECKBOX"===u?(function(e,r){null!==r&&r.length>0&&r.map(function(e,r){e.rowCols.map(function(e,r){e.checked=!1})})}(0,e.quRows),a.anMatrixCheckboxes=[]):"MATRIX_INPUT"===u?(function(e,r){null!==r&&r.length>0&&r.map(function(e,r){e.rowCols.map(function(e,r){e.answerValue=null})})}(0,e.quRows),a.anMatrixFbks=[]):"MATRIX_SCALE"===u?(o(e,e.quRows),a.anMatrixScales=[]):"MATRIX_SLIDER"===u&&(o(e,e.quRows),a.anMatrixScales=[]),e.anQuestion=a},t("4USb")},Rp86:function(e,r,t){t("bBy9"),t("FlQf"),e.exports=t("fXsU")},TdIW:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.dwSurveyJsonBySurveyId=function(e){return(0,o.default)({url:"/api/dwsurvey/none/v6/dw-answer-survey/survey-json-by-survey-id.do",method:"get",params:e})},r.dwSaveSurveyAnswerJson=function(e){return(0,o.default)({url:"/api/dwsurvey/none/v6/dw-answer-survey/save-survey-answer.do",method:"post",data:e})},r.dwSurveyAnswerById=function(e){return(0,o.default)({url:"/api/dwsurvey/none/v6/dw-answer-survey/get-survey-answer.do",method:"get",params:e})},r.dwSurveyAnswerCheckPwd=function(e){return(0,o.default)({url:"/api/dwsurvey/none/v6/dw-answer-survey/check-answer-pwd.do",method:"get",params:e})};var n,o=(n=t("t3Un"))&&n.__esModule?n:{default:n}},VJsP:function(e,r,t){"use strict";var n=t("2GTP"),o=t("Y7ZC"),a=t("JB68"),u=t("sNwI"),s=t("NwJ3"),i=t("tEej"),l=t("IP1Z"),c=t("fNZA");o(o.S+o.F*!t("TuGD")(function(e){Array.from(e)}),"Array",{from:function(e){var r,t,o,d,p=a(e),f="function"==typeof this?this:Array,w=arguments.length,v=w>1?arguments[1]:void 0,y=void 0!==v,h=0,A=c(p);if(y&&(v=n(v,w>2?arguments[2]:void 0,2)),void 0==A||f==Array&&s(A))for(t=new f(r=i(p.length));r>h;h++)l(t,h,y?v(p[h],h):p[h]);else for(d=A.call(p),t=new f;!(o=d.next()).done;h++)l(t,h,y?u(d,v,[o.value,h],!0):o.value);return t.length=h,t}})},VKFn:function(e,r,t){t("bBy9"),t("FlQf"),e.exports=t("ldVq")},VeDC:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.parseSurvey=function(e){return null!==e&&(e.hasOwnProperty("surveyNameObj")||(e.surveyNameObj={dwHtml:e.surveyName,dwText:e.surveyNameText,dwPlaceholder:"请输入问卷标题"}),s(e),i(e.questions,!0),e.surveyTest="",e.curEditObj=[{itemClick:!1}],e.tempDataType="none",e.hasOwnProperty("dwId")||(e.dwId=(0,n.v4)()),e.showSurvey=!0,e.hasOwnProperty("surveyStyle")||(e.surveyStyle=(0,o.getDefaultSurveyStyle)()),e.surveyStyle.hasOwnProperty("showQuScoreNum")||(e.surveyStyle.showQuScoreNum=!1),e.clientBrowser={windowWidth:0,matrixWidth:0},e.hasOwnProperty("designLayout")||(e.designLayout="TB"),e.watchEvent="oooww",e.watchEventScrollToId="oooww",e.scrollToQuIndex=null,e.surveyTypeSimpleName="问卷",(0,u.getSurveyTypeSimpleName)(e),e.surveyFocusObj={rightFocusTab:"surveySet",focusQuIndex:null}),e},r.parseSurveyDetail=s,r.parseQuestions=i,r.parseQuestion=l,r.initQuestionModels=function(e){return null!==e&&e.length>0&&e.map(function(e,r){e.showQu=!0,e.logicIsHide=!1,c(e),w(e)}),e},r.parseQuOptionTypeByQu=function(e,r){var t=e.quType;"RADIO"!==t&&"CHECKBOX"!==t&&"ORDERQU"!==t&&"MULTIFILLBLANK"!==t&&"SCORE"!==t||p(r)},r.parseQuOptionType1Item=p,r.resetQuestion=function(e){e.dwId=(0,n.v4)();var r=e.quType;"RADIO"===r?f(e,e.quRadios):"CHECKBOX"===r?f(e,e.quCheckboxs):"ORDERQU"===r?f(e,e.quOrderbys):"MULTIFILLBLANK"===r?f(e,e.quMultiFillblanks):"SCORE"===r?f(e,e.quScores):"MATRIX_RADIO"!==r&&"MATRIX_CHECKBOX"!==r&&"MATRIX_INPUT"!==r&&"MATRIX_SCALE"!==r&&"MATRIX_SLIDER"!==r||function(e,r,t){null!==r&&r.length>0&&r.forEach(function(e,r){e.dwId=(0,n.v4)()}),null!==t&&t.length>0&&t.forEach(function(e,r){e.dwId=(0,n.v4)()})}(0,e.quRows,e.quCols)},r.buildMatrixOption=w;var n=t("4USb"),o=t("XDW4"),a=t("1y4o"),u=t("heCB");function s(e){var r=e.surveyDetail;if(null!==r){var t=null!==r.surveyNoteText?r.surveyNoteText:"";e.surveyDetail.hasOwnProperty("surveyNodeObj")||(e.surveyDetail.surveyNodeObj={dwHtml:r.surveyNote,dwText:t,dwPlaceholder:"请输入问卷介绍"})}e.surveyDetail.effective_model=1===e.surveyDetail.effective,e.surveyDetail.effectiveIp_model=1===e.surveyDetail.effectiveIp,e.surveyDetail.refresh_model=1===e.surveyDetail.refresh,e.surveyDetail.rule_model=3===e.surveyDetail.rule,e.surveyDetail.ynEndNum_model=1===e.surveyDetail.ynEndNum,e.surveyDetail.endNum_model=e.surveyDetail.endNum,e.surveyDetail.ynEndTime_model=1===e.surveyDetail.ynEndTime,e.surveyDetail.hasOwnProperty("dwId")||(e.surveyDetail.dwId=(0,n.v4)()),e.hasOwnProperty("surveyAttrs")||(e.surveyAttrs={anBroAttr:{enabled:!1,anNum:1},anIpAttr:{enabled:!1,anNum:1},anRefreshAttr:{randomCode:!0},anPwdAttr:{enabled:!1,anPwdCode:null},anEndNumAttr:{enabled:!1,endNum:null},anEndTimeAttr:{enabled:!1,endTime:null}}),e.surveyAttrs.hasOwnProperty("anStartTimeAttr")||(e.surveyAttrs.anStartTimeAttr={enabled:!1,startTime:null}),e.surveyAttrs.hasOwnProperty("scoreAttr")||(e.surveyAttrs.scoreAttr={enabled:!1,maxScore:0,showSumScore:{enabled:!0,showContent:"sumAndDetail"}}),e.surveyAttrs.scoreAttr.hasOwnProperty("showSumScore")||(e.surveyAttrs.scoreAttr.showSumScore={enabled:!0,showContent:"sumAndDetail"}),e.surveyAttrs.hasOwnProperty("opoqAttr")||(e.surveyAttrs.opoqAttr={enabled:!1})}function i(e,r){return null!==e&&e.length>0&&e.forEach(function(e,t){e.showQu=!0,e.logicIsHide=!1,l(e,r)}),e}function l(e,r){var t=null!==e.quName?e.quName:e.quTitle;e.hasOwnProperty("quTitleObj")||(e.quTitleObj={dwHtml:e.quTitle,dwText:t,dwPlaceholder:"请输入题目标题",isNew:!1});var o=e.quNote;e.hasOwnProperty("quNoteObj")||(e.quNoteObj={dwHtml:o,dwText:o,dwPlaceholder:"请输入题目备注",isNew:!1}),e.hasOwnProperty("questionLogics")&&null!==e.questionLogics||(e.questionLogics=[]),r&&!e.hasOwnProperty("dwId")&&(e.dwId=(0,n.v4)()),c(e);var a=e.quType;"RADIO"===a?function(e){e.quTypeName="单选题",0===e.cellCount&&(e.cellCount=2),d(e,e.quRadios)}(e):"CHECKBOX"===a?function(e){e.quTypeName="多选题",0===e.cellCount&&(e.cellCount=2),d(e,e.quCheckboxs),e.quAttr.hasOwnProperty("scoreAttr")||(e.quAttr.scoreAttr={maxScore:0,designShowScoreNum:!1,allRight:{enabled:!1,scoreNum:0}})}(e):"ORDERQU"===a?function(e){e.quTypeName="排序题",d(e,e.quOrderbys)}(e):"MULTIFILLBLANK"===a?function(e){e.quTypeName="多项填空题",d(e,e.quMultiFillblanks)}(e):"SCORE"===a?function(e){e.quTypeName="评分题",d(e,e.quScores)}(e):"FILLBLANK"===a?function(e){e.quTypeName="填空题",e.hasOwnProperty("placeholder")||(e.placeholder="请输入"),e.hasOwnProperty("step")||(e.step="00:05");var r={commonAttr:{checkType:null,placeholder:"",defaultValue:"",inputRow:1,minlength:0,maxlength:119,isRequired:1},dateTimeAttr:{timeRange:{range:null,step:null},dateFormat:null,attrs:[]},numAttr:{min:null,max:null}};c(e),e.quAttr.hasOwnProperty("inputAttr")&&void 0!==e.quAttr.inputAttr||(e.quAttr.inputAttr=r),e.quAttr.inputAttr.hasOwnProperty("dateTimeAttr")||(e.quAttr.inputAttr.dateTimeAttr=r.dateTimeAttr);var t=e.checkType;"DATE"===t?(e.quAttr.inputAttr.commonAttr.checkType="DATE",console.debug("question.quAttr.inputProp",e.quAttr.inputProp),e.quAttr.inputAttr.dateTimeAttr.dateFormat=3):"TIME"===t?(e.quAttr.inputAttr.commonAttr.checkType="TIME",e.quAttr.inputAttr.dateTimeAttr.dateFormat=7):"EMAIL"===t?e.quAttr.inputAttr.commonAttr.checkType="EMAIL":"PHONENUM"===t?e.quAttr.inputAttr.commonAttr.checkType="PHONE":"IDENTCODE"===t?e.quAttr.inputAttr.commonAttr.checkType="IDENT_CODE":"DIGITS"===t&&(e.quAttr.inputAttr.commonAttr.checkType="DIGITS")}(e):"UPLOADFILE"===a?function(e){e.quTypeName="上传题",e.hasOwnProperty("placeholder")||(e.placeholder="请输入"),e.hasOwnProperty("step")||(e.step="00:05")}(e):"PAGETAG"===a?e.quTypeName="分页组件":"PARAGRAPH"===a?e.quTypeName="分段组件":"MATRIX_RADIO"===a?e.quTypeName="矩阵单选题":"MATRIX_CHECKBOX"===a?e.quTypeName="矩阵多选题":"MATRIX_INPUT"===a?e.quTypeName="矩阵填空题":"MATRIX_SCALE"===a?e.quTypeName="矩阵量表题":"MATRIX_SLIDER"===a&&(e.quTypeName="矩阵滑块题"),e.hasOwnProperty("validateObj")||(e.validateObj={errorText:"",isOk:!0}),e.itemClick=!1,e.hasOwnProperty("question")||(e.quFocusObj={quFocus:!1,quSetShow:!1,quLogicShow:!1,quMoreOptionShow:!1,quMoreOptionShowEdit:!1,quScorePopoverShow:!1,quScaleTextPopoverShow:!1,quMoreOptionColShow:!1})}function c(e){e.hasOwnProperty("quAttr")?e.quAttr.hasOwnProperty("isRequired")||(e.quAttr.isRequired=!0):e.quAttr={isRequired:!0},e.quAttr.hasOwnProperty("scoreAttr")||(e.quAttr.scoreAttr={maxScore:0,designShowScoreNum:!1});var r=e.quType;return"CHECKBOX"!==r||e.quAttr.scoreAttr.hasOwnProperty("allRight")||(e.quAttr.scoreAttr.allRight={enabled:!1,scoreNum:0}),e.quAttr.hasOwnProperty("showQuNote")||(e.quAttr.showQuNote=!1),"SCORE"===r&&(e.quAttr.hasOwnProperty("scoreQuAttr")||(e.quAttr.scoreQuAttr={max:5,texts:[]})),"MATRIX_SCALE"===r&&(e.quAttr.hasOwnProperty("scaleAttr")||(e.quAttr.scaleAttr={min:0,max:10,showLrText:!0})),"MATRIX_SLIDER"===r&&(e.quAttr.hasOwnProperty("sliderAttr")||(e.quAttr.sliderAttr={min:0,max:100,step:1,showLrText:!0})),e}function d(e,r){null!==r&&r.length>0&&r.forEach(function(r,t){e.hasOwnProperty("dwId")&&!r.hasOwnProperty("dwId")&&(r.dwId=(0,n.v4)()),p(r)})}function p(e){var r=null!==e.optionTitle?e.optionTitle:e.optionName,t=null!==e.optionName?e.optionName:r;e.hasOwnProperty("optionTitleObj")||(e.optionTitleObj={dwHtml:t,dwText:r,dwPlaceholder:"请输入选项内容"}),e.hasOwnProperty("dateAttrs")||(e.dateAttrs=[]),e.hasOwnProperty("checked")||(e.checked=!1),e.hasOwnProperty("orderIndex")||(e.orderIndex=0);e.hasOwnProperty("inputAttr")||(e.inputAttr={commonAttr:{checkType:null,placeholder:"",defaultValue:"",inputRow:1,minlength:0,maxlength:119,isRequired:1},dateTimeAttr:{timeRange:{range:null,step:null},dateFormat:null,attrs:[]},numAttr:{min:null,max:null}}),e.hasOwnProperty("showOptionNote")||(e.showOptionNote=0),e.hasOwnProperty("isRequired")||(e.isRequired=1),e.hasOwnProperty("otherText")||(e.otherText=null),e.validateObj={errorText:"",isOk:!0},e.hasOwnProperty("scoreNum")||(e.scoreNum=null)}function f(e,r){null!==r&&r.length>0&&(r.forEach(function(e,r){e.dwId=(0,n.v4)()}),d(e,r))}function w(e){var r=e.quType;if(!("MATRIX_RADIO"!==r&&"MATRIX_CHECKBOX"!==r&&"MATRIX_INPUT"!==r&&"MATRIX_SCALE"!==r&&"MATRIX_SLIDER"!==r||e.hasOwnProperty("quRows")&&null!==e.quRows&&0!==e.quRows.length)){for(var t=[],o=0;o<3;o++){var u={dwId:(0,n.v4)(),optionTitleObj:{dwHtml:"行选项"+o,dwText:"行选项"+o,dwPlaceholder:"请输入选项内容"}};"MATRIX_SCALE"!==r&&"MATRIX_SLIDER"!==r||(u.lr={left:{optionTitleObj:{dwHtml:"极不可能",dwText:"不可能",dwPlaceholder:"请输入选项内容"}},right:{optionTitleObj:{dwHtml:"极有可能",dwText:"极有可能",dwPlaceholder:"请输入选项内容"}}}),t.push(u)}e.quRows=t}if("MATRIX_RADIO"===r||"MATRIX_CHECKBOX"===r||"MATRIX_INPUT"===r){if(!e.hasOwnProperty("quCols")||null===e.quCols||0===e.quCols.length){for(var s=[{dwId:(0,n.v4)(),optionTitleObj:{dwHtml:"",dwText:"",dwPlaceholder:""},scoreNum:null,tempEmptyOption:!0}],i=0;i<3;i++){var l={dwId:(0,n.v4)(),optionTitleObj:{dwHtml:"列选项"+i,dwText:"列选项"+i,dwPlaceholder:"请输入选项内容"},scoreNum:null,tempEmptyOption:!1};s.push(l)}e.quCols=s}(0,a.buildMatrixQuRowCols)(e)}}},XDW4:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.dwSurveyColorUtils=void 0;var n=u(t("rfXi")),o=u(t("sk9p"));r.getDefaultSurveyStyle=function(){var e=a.dwSurveyRootStyle.answerThemeStyle;return{pageTopImg:{enabled:!1,src:null,httpSrc:null},pageBgImg:{enabled:!1,src:null,httpSrc:null},logoImg:{enabled:!1,src:null,httpSrc:null,position:"topLogoRight"},pageBgColor:e.dwAnswerPageBgColor,pageThemeColor:e.dwAnswerPrimaryColor,pageThemeColor1:e.dwAnswerPrimaryColor1,logoBgColor:e.dwAnswerLogoBgColor,progressColor:e.dwAnswerProgressColor,showQuNum:!0,showProgressbar:!0,showPageHeader:!0,showQuTypeName:!0,showSurveyTitle:!0,showSurveyNote:!0,showQuScoreNum:!1}},r.generateNumbers=function(e,r){return(0,n.default)({length:r-e+1},function(r,t){return e+t})};var a=t("1TBi");function u(e){return e&&e.__esModule?e:{default:e}}r.dwSurveyColorUtils={rgbToHex:function(e){if(void 0!==e&&null!==e){var r=e.replace("rgb","").replace("(","").replace(")","").split(",");return"#"+parseInt(r[0]).toString(16)+parseInt(r[1]).toString(16)+parseInt(r[2]).toString(16)}return null},hexToRgb:function(e){if(void 0!==e&&null!==e){var r=e.replace("#","");if(r.length%3)return"hex格式不正确！";var t=r.length/3,n=6/r.length;return"rgb("+Math.pow(parseInt("0x"+r.substring(0,t)),n)+", "+Math.pow(parseInt("0x"+r.substring(t,2*t)),n)+", "+Math.pow(parseInt("0x"+r.substring(2*t)),n)+")"}return null},rgbToHsl:function(e,r,t){e/=255,r/=255,t/=255;var n=Math.max(e,r,t),o=Math.min(e,r,t),a=(n+o)/2,u=(n+o)/2,s=(n+o)/2;if(n===o)a=u=0;else{var i=n-o;switch(u=s>.5?i/(2-n-o):i/(n+o),n){case e:a=(r-t)/i+(r<t?6:0);break;case r:a=(t-e)/i+2;break;case t:a=(e-r)/i+4}a/=6}return[a,u,s]},generateSimilarColor:function(e,r,t,n){var a=e.match(/\d+/g).map(Number),u=(0,o.default)(a,3),s=u[0],i=u[1],l=u[2],c=this.rgbToHsl(s,i,l),d=(0,o.default)(c,3);return function(e,r,t,o,a){return t+=a,t*=100,!n&&t>100&&(t=98),"hsl("+360*e+", "+100*(r+=o)+"%, "+t+"%)"}(d[0],d[1],d[2],r,t)},isWhiteColor:function(e){var r=[e.red,e.green,e.blue].map(function(e){return e/255});return console.debug("rgb",r),r.every(function(e){return e>.95})},hslToRgb:function(e,r,t){function n(e,r,t){return t<0&&(t+=1),t>1&&(t-=1),t<1/6?e+6*(r-e)*t:t<.5?r:t<2/3?e+(r-e)*(2/3-t)*6:e}var o=t<.5?t*(1+r):t+r-t*r,a=2*t-o,u=n(a,o,e+1/3),s=n(a,o,e),i=n(a,o,e-1/3);return[Math.round(255*u),Math.round(255*s),Math.round(255*i)]}}},ZXcj:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.validateQuestionsBoolBySurvey=function(e){var r=e.questions;e.scrollToQuIndex=null;var t=!0;return u(r,!0),r.forEach(function(r,n){if(r.showQu&&!r.logicIsHide&&!r.validateObj.isOk)return null===e.scrollToQuIndex&&(e.scrollToQuIndex=n),t=!1,!1}),null!==e.scrollToQuIndex&&(e.watchEventScrollToId=(0,a.v4)()),t},r.validateQuestionsBool=function(e){var r=!0;return u(e,!0),e.forEach(function(e,t){if(e.showQu&&!e.logicIsHide&&!e.validateObj.isOk)return r=!1,!1}),r},r.submitValidateQuestions=u,r.validateQuestions=function(e){return null!==e&&e.length>0&&e.forEach(function(e,r){s(e)}),e},r.validateQuestion=s;var n=t("+1tm"),o=t("BNG+"),a=t("4USb");function u(e,r){return null!==e&&e.length>0&&e.forEach(function(e,t){e.showOptionError=r,s(e)}),e}function s(e){if(null!==e){var r=e.quType;e.showQu&&!e.logicIsHide&&(e.validateObj={errorText:"此题必答",isOk:!0,isAnswerOk:!1},"PAGETAG"!==r&&"PARAGRAPH"!==r&&("RADIO"===r?function(e){var r=[],t=e.validateObj,n=0;if((0,o.getQuestionAnswerData)(e),e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anRadio")&&(n=e.anQuestion.anRadio.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<=0&&r.push("此题必答"),e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anRadio"))for(var a=e.anQuestion.anRadio,u=e.quRadios,s=0;s<u.length;s++){var l=u[s];if(l.validateObj={errorText:"",isOk:!0},l.dwId===a.optionDwId&&l.hasOwnProperty("showOptionNote")&&1===l.showOptionNote&&l.hasOwnProperty("inputAttr")){var c=[];i(e,l.inputAttr,c,a.otherText),c.length>0&&e.hasOwnProperty("showOptionError")&&e.showOptionError&&(l.validateObj.errorText=c.join("，"),l.validateObj.isOk=!1,t.isOk=!1)}}t.errorText="",r.length>0&&(t.errorText=r.join("，"),t.isOk=!1),e.validateObj=t}(e):"CHECKBOX"===r?function(e){var r=[],t=e.validateObj,n=0;if(e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anCheckboxs")&&(n=e.anQuestion.anCheckboxs.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<=0&&r.push("此题必答"),t.isOk){if(e.hasOwnProperty("minLimit")){var o=e.minLimit;null!=o&&o>0&&n<o&&r.push("至少需要选择"+o+"个选项")}if(e.hasOwnProperty("maxLimit")){var a=e.maxLimit;null!=a&&a>0&&n>a&&r.push("最多选择"+a+"个选项")}}if(e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anCheckboxs"))for(var u=e.anQuestion.anCheckboxs,s=e.quCheckboxs,l=0;l<s.length;l++){var c=s[l];c.validateObj={errorText:"",isOk:!0};for(var d=0;d<u.length;d++){var p=u[d];if(c.dwId===p.optionDwId&&c.hasOwnProperty("showOptionNote")&&1===c.showOptionNote&&c.hasOwnProperty("inputAttr")){var f=[];i(e,c.inputAttr,f,c.otherText),f.length>0&&e.hasOwnProperty("showOptionError")&&e.showOptionError&&(c.validateObj.errorText=f.join("，"),c.validateObj.isOk=!1,t.isOk=!1)}}}t.errorText="",r.length>0&&(t.errorText=r.join("，"),t.isOk=!1),e.validateObj=t}(e):"ORDERQU"===r?function(e){var r=e.quOrderbys,t=e.validateObj,n=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anOrders")&&(n=e.anQuestion.anOrders.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<r.length&&(t.errorText="此题必答",t.isOk=!1),e.validateObj=t}(e):"MULTIFILLBLANK"===r?function(e){var r=[],t=e.quMultiFillblanks,n=e.validateObj,a=0;if((0,o.getQuestionAnswerData)(e),e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anMFbks")&&(a=e.anQuestion.anMFbks.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&a<t.length&&r.push("此题必答"),e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anMFbks"))for(var u=e.anQuestion.anMFbks,s=e.quMultiFillblanks,l=0;l<s.length;l++){var c=s[l];c.validateObj={errorText:"",isOk:!0};for(var d=0;d<u.length;d++){var p=u[d];if(c.dwId===p.optionDwId&&c.hasOwnProperty("inputAttr")){var f=[];i(e,c.inputAttr,f,p.answer),f.length>0&&e.hasOwnProperty("showOptionError")&&e.showOptionError&&(c.validateObj.errorText=f.join("，"),c.validateObj.isOk=!1,n.isOk=!1)}}}n.errorText="",r.length>0&&(n.errorText=r.join("，"),n.isOk=!1),e.validateObj=n}(e):"SCORE"===r?function(e){var r=e.quScores,t=e.validateObj,n=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anScores")&&(n=e.anQuestion.anScores.length),console.debug("quScores.answerSize",n),console.debug("quScores.length",r.length),console.debug("question.anQuestion",e.anQuestion),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<r.length&&(t.errorText="此题必答",t.isOk=!1),e.validateObj=t}(e):"FILLBLANK"===r?function(e){var r=e.validateObj,t=null;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anFbk")&&(t=e.anQuestion.anFbk.answer);var n=[];if(null!==t&&void 0!==t||(t=""),e.hasOwnProperty("quAttr")){var o=e.quAttr;if(o.hasOwnProperty("isRequired")&&o.isRequired&&t.length<=0&&n.push("此题必答"),o.hasOwnProperty("inputAttr"))i(e,o.inputAttr,n,t),n.length>0&&(r.errorText=n.join("，"),r.isOk=!1)}e.validateObj=r}(e):"UPLOADFILE"===r?function(e){var r=e.validateObj,t=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anUploadFiles")&&(t=e.anQuestion.anUploadFiles.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&t<=0&&(r.errorText="此题必答",r.isOk=!1),e.validateObj=r}(e):"MATRIX_RADIO"===r?function(e){var r=e.validateObj,t=e.quRows,n=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anMatrixRadios")&&(n=e.anQuestion.anMatrixRadios.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<t.length&&(r.errorText="此题必答",r.isOk=!1),e.validateObj=r}(e):"MATRIX_CHECKBOX"===r?function(e){var r=e.validateObj,t=e.quRows,n=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anMatrixCheckboxes")&&(n=e.anQuestion.anMatrixCheckboxes.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<t.length&&(r.errorText="此题必答",r.isOk=!1),e.validateObj=r}(e):"MATRIX_INPUT"===r?function(e){var r=e.validateObj,t=!0;e.quRows.map(function(e,r){e.rowCols.map(function(e,r){if(!e.tempEmptyOption){var n=e.answerValue;null!==n&&void 0!==n&&""!==n||(t=!1)}})}),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&!t&&(r.errorText="此题必答",r.isOk=!1),e.validateObj=r}(e):"MATRIX_SCALE"===r?l(e):"MATRIX_SLIDER"===r&&l(e)),e.validateObj.isOk&&(e.validateObj.isAnswerOk=!0))}return e}function i(e,r,t,o){if(r.hasOwnProperty("commonAttr")){var a=r.commonAttr;if(a.hasOwnProperty("isRequired")&&1===a.isRequired&&(void 0===o||null===o||"null"===o||o.length<=0)&&(t.push("此项必答"),e.showOptionError=!0),void 0!==o&&null!==o&&o.length>0&&a.hasOwnProperty("checkType")||a.hasOwnProperty("isRequired")&&1===a.isRequired){e.showOptionError=!0;var u=a.checkType;if(null!==u){var s=(0,n.dwCheckValue)(u,o,null);"DATE"===u&&(s.isOK=!0),s.isOK||t.push(s.msg)}if("NO"===u||"EMAIL"===u||"URL"===u||"UNSTRCN"===u||"STRCN"===u){var i=o.length;if(a.hasOwnProperty("minlength")){var l=a.minlength;i<l&&t.push("最少输入"+l+"字")}if(a.hasOwnProperty("maxlength")){var c=a.maxlength;i>c&&t.push("最多输入"+c+"字")}}else if("NUM"===u||"DIGITS"===u){var d=r.numAttr,p=parseFloat(o);if(d.hasOwnProperty("min")){var f=d.min;null!==f&&f>0&&p<f&&t.push("最小值不小于"+f)}if(d.hasOwnProperty("max")){var w=d.max;null!==w&&w>0&&p>w&&t.push("最大值不大于"+w)}}}}}function l(e){var r=e.validateObj,t=e.quRows,n=0;e.hasOwnProperty("anQuestion")&&e.anQuestion.hasOwnProperty("anMatrixScales")&&(n=e.anQuestion.anMatrixScales.length),e.hasOwnProperty("quAttr")&&e.quAttr.hasOwnProperty("isRequired")&&e.quAttr.isRequired&&n<t.length&&(r.errorText="此题必答",r.isOk=!1),e.validateObj=r}},fXsU:function(e,r,t){var n=t("5K7Z"),o=t("fNZA");e.exports=t("WEpk").getIterator=function(e){var r=o(e);if("function"!=typeof r)throw TypeError(e+" is not iterable!");return n(r.call(e))}},heCB:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.surveyPageUtils=void 0,r.getSurveyJsonBySurveyId=a,r.getDesignSurveyJsonBySurveyId=function(e,r){a(e,r,function(){u(e,r)})},r.getQuerySurvey=u,r.getSurveyTypeSimpleName=function(e){null!==e&&("survey"===e.surveyType?e.surveyTypeSimpleName="问卷":"exam"===e.surveyType?e.surveyTypeSimpleName="试卷":"vote"===e.surveyType?e.surveyTypeSimpleName="投票":"satisfaction"===e.surveyType?e.surveyTypeSimpleName="满意度":"satisfaction"===e.surveyType?e.surveyTypeSimpleName="测评":"360eval"===e.surveyType?e.surveyTypeSimpleName="360评估":e.surveyTypeSimpleName="调查")};var n=t("CG6M"),o=t("VeDC");function a(e,r,t){(0,n.surveyJsonDesignBySurveyId)(e).then(function(e){console.debug("surveyJsonBySurveyId",e);var n=e.data;if(200===n.resultCode){var a=n.data;null!==a&&a.hasOwnProperty("surveyJsonText")&&null!==a.surveyJsonText&&""!==a.surveyJsonText?r((0,o.parseSurvey)(JSON.parse(a.surveyJsonText))):t()}})}function u(e,r){(0,n.querySurveyAll)(e).then(function(e){console.debug("querySurveyAll",e);var t=e.data;if(200===t.resultCode){console.debug("httpResult.data",t.data);var n=(0,o.parseSurvey)(t.data);console.debug("parseResultData",n),r(n)}})}r.surveyPageUtils={pageSize:function(e){var r=1;return e.questions.forEach(function(e,t){"PAGETAG"===e.quType&&r++}),r},quInPageNum:function(e,r){var t=0;return e.questions.forEach(function(e,n){"PAGETAG"===e.quType&&n<=r&&t++}),t}}},"k/8l":function(e,r,t){e.exports={default:t("VKFn"),__esModule:!0}},ldVq:function(e,r,t){var n=t("QMMT"),o=t("UWiX")("iterator"),a=t("SBuE");e.exports=t("WEpk").isIterable=function(e){var r=Object(e);return void 0!==r[o]||"@@iterator"in r||a.hasOwnProperty(n(r))}},mGn2:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n,o=(n=t("gDS+"))&&n.__esModule?n:{default:n};r.buildSurveyLocalStorageKey=function(e,r){return e+"_"+r},r.saveJsonObj2LocalStorage=function(e,r){localStorage.setItem(e,(0,o.default)(r))},r.getLocalStorageByKey=function(e){return localStorage.getItem(e)},r.getLocalStorageByKeyword=function(e){for(var r=[],t=0;t<localStorage.length;t++){var n=localStorage.key(t);n.includes(e)&&r.push(n)}return r}},pC62:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.dwSurveyAnswerLogicLoad=function(e){e.surveyLogicControl={hideQus:[]};var r=e.questions;void 0!==r&&r.map(function(r,t){a(e,t)})},r.dwSurveyAnswerLogic=a,r.surveyLogicControlExe=s,r.quLogicIsHide=i;var n=t("MBF8"),o=t("4USb");function a(e,r){var t=e.questions;e.hasOwnProperty("surveyLogicControl")||(e.surveyLogicControl={hideQus:[]}),void 0!==t&&u(e,t[r]),s(e),e.watchEvent=(0,o.v4)(),console.debug("watchEvent",e.watchEvent)}function u(e,r){var t=r.quType;function n(e,r,t){var n=t.dwId,o=t.logicType,a=t.skQuId;if("GO"===o){var u=function(e,r,t){for(var n=[],o=!1,a=e.questions,u=0;u<a.length;u++){var s=a[u].dwId;if(s===r){o=!1;break}s===t?o=!0:o&&n.push(s)}return n}(e,a,r.dwId);e.surveyLogicControl.hideQus.push({idType:"quId",hideQuDwId:u,trigger:n})}else if("SHOW"===o){var s=e.surveyLogicControl.hideQus;e.surveyLogicControl.hideQus=s.filter(function(e){return e.trigger!==n})}}function o(r){var t=r.dwId,n=r.logicType,o=r.skQuId;if("GO"===n){var a=e.surveyLogicControl.hideQus;e.surveyLogicControl.hideQus=a.filter(function(e){return e.trigger!==t})}else"SHOW"===n&&e.surveyLogicControl.hideQus.push({idType:"quId",hideQuDwId:o,trigger:t})}r.questionLogics.forEach(function(a,u){r.hasOwnProperty("anQuestion")&&void 0!==r.anQuestion?"RADIO"===t?function(e,r,t){var a=r.anQuestion,u=t.cgQuItemId;a.hasOwnProperty("anRadio")&&a.anRadio.hasOwnProperty("optionDwId")&&u.includes(a.anRadio.optionDwId)?n(e,r,t):o(t)}(e,r,a):"CHECKBOX"===t?function(e,r,t){var a=r.anQuestion,u=t.cgQuItemId;if(a.hasOwnProperty("anCheckboxs")&&null!=a.anCheckboxs&&a.anCheckboxs.length>0){var s=!1;a.anCheckboxs.forEach(function(e,r){e.hasOwnProperty("optionDwId")&&u.includes(e.optionDwId)&&(s=!0)}),s?n(e,r,t):o(t)}else o(t)}(e,r,a):"ORDERQU"===t?function(e,r,t){var a=r.anQuestion,u=t.cgQuItemId;if(a.hasOwnProperty("anOrders")&&null!=a.anOrders&&a.anOrders.length>0){var s=!1;a.anOrders.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")&&u.includes(e.optionDwId)){var n=e.orderNum,o=t.geLe,a=t.scoreNum;"LTE"===o&&n>=a?s=!0:"GTE"===o&&n<=a?s=!0:"LT"===o&&n>a?s=!0:"GT"===o&&n<a&&(s=!0)}}),s?n(e,r,t):o(t)}else o(t)}(e,r,a):"MULTIFILLBLANK"===t?function(e,r,t){var a=r.anQuestion,u=t.cgQuItemId;if(a.hasOwnProperty("anMFbks")&&null!=a.anMFbks&&a.anMFbks.length>0){var s=!1;a.anMFbks.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")&&u.includes(e.optionDwId)){var t=e.answer;void 0!==t&&null!==t&&""!==t&&(s=!0)}}),s?n(e,r,t):o(t)}else o(t)}(e,r,a):"SCORE"===t?function(e,r,t){var a=r.anQuestion,u=t.cgQuItemId;if(a.hasOwnProperty("anScores")&&null!=a.anScores&&a.anScores.length>0){var s=!1;a.anScores.forEach(function(e,r){if(e.hasOwnProperty("optionDwId")&&u.includes(e.optionDwId)){var n=e.answerScore,o=t.geLe,a=t.scoreNum;"LTE"===o&&n<=a?s=!0:"GTE"===o&&n>=a?s=!0:"LT"===o&&n<a?s=!0:"GT"===o&&n>a&&(s=!0)}}),s?n(e,r,t):o(t)}else o(t)}(e,r,a):"FILLBLANK"===t?function(e,r,t){var a=r.anQuestion;a.hasOwnProperty("anFbk")&&a.anFbk.hasOwnProperty("answer")&&void 0!==a.anFbk.answer&&null!==a.anFbk.answer&&""!==a.anFbk.answer?n(e,r,t):o(t)}(e,r,a):"UPLOADFILE"===t&&function(e,r,t){var a=r.anQuestion;console.debug("anUploadFiles",a.anUploadFiles),a.hasOwnProperty("anUploadFiles")&&null!=a.anUploadFiles&&a.anUploadFiles.length>0?n(e,r,t):o(t)}(e,r,a):o(a)})}function s(e){var r=e.questions;if(void 0!==r){var t=!1,o=1;r.map(function(r,a){var s=i(e,r);!r.logicIsHide&&s&&(e.hasOwnProperty("firstLoadAnswer")&&e.firstLoadAnswer||(0,n.clearQuestionAnswer)(r),u(e,r)),r.logicIsHide=s;var l=r.quType;r.pageIndex>o&&(o=r.pageIndex,t=!1),r.pageIndex===o&&(r.logicIsHide||(t=!0),"PAGETAG"===l&&(r.logicIsHide=!t))})}}function i(e,r){var t=!1;if(e.hasOwnProperty("surveyLogicControl")&&e.surveyLogicControl.hasOwnProperty("hideQus"))for(var n=e.surveyLogicControl.hideQus,o=0;o<n.length;o++)if(n[o].hideQuDwId.includes(r.dwId)){t=!0;break}return t}},rfXi:function(e,r,t){e.exports={default:t("0tVQ"),__esModule:!0}},sk9p:function(e,r,t){"use strict";r.__esModule=!0;var n=a(t("k/8l")),o=a(t("FyfS"));function a(e){return e&&e.__esModule?e:{default:e}}r.default=function(e,r){if(Array.isArray(e))return e;if((0,n.default)(Object(e)))return function(e,r){var t=[],n=!0,a=!1,u=void 0;try{for(var s,i=(0,o.default)(e);!(n=(s=i.next()).done)&&(t.push(s.value),!r||t.length!==r);n=!0);}catch(e){a=!0,u=e}finally{try{!n&&i.return&&i.return()}finally{if(a)throw u}}return t}(e,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}}]);