package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseServiceImpl;
import net.diaowen.dwsurvey.dao.TaskGridDao;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.service.TaskGridManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 任务网格管理服务实现类
 * <AUTHOR> Team
 */
@Service("taskGridManager")
public class TaskGridManagerImpl extends BaseServiceImpl<TaskGrid, String> implements TaskGridManager {

    @Autowired
    private TaskGridDao taskGridDao;

    @Override
    public void setBaseDao() {
        this.baseDao = taskGridDao;
    }

    @Override
    @Transactional
    public void save(TaskGrid grid) {
        if (grid.getId() == null || grid.getId().isEmpty()) {
            grid.setCreateTime(new Date());
        }
        grid.setUpdateTime(new Date());
        super.save(grid);
    }

    @Override
    public Page<TaskGrid> findByCondition(Page<TaskGrid> page, String keyword, String parentId) {
        // This implementation needs to be adjusted to match the interface
        return taskGridDao.findByCondition(page, keyword, keyword, null, 1);
    }

    @Override
    public TaskGrid findByGridCode(String gridCode) {
        return taskGridDao.findByGridCode(gridCode);
    }

    @Override
    public List<TaskGrid> findByParentGridCode(String parentGridCode) {
        return taskGridDao.findByParentGridCode(parentGridCode);
    }

    @Override
    public List<TaskGrid> findByGridLevel(String gridLevel) {
        return taskGridDao.findByGridLevel(gridLevel);
    }

    @Override
    public List<TaskGrid> findActiveGrids() {
        return taskGridDao.findActiveGrids();
    }

    @Override
    public TaskGrid findGridByPoint(Double longitude, Double latitude) {
        return taskGridDao.findGridByPoint(longitude, latitude);
    }

    @Override
    public List<TaskGrid> findChildGrids(String parentGridId) {
        return taskGridDao.findChildGrids(parentGridId);
    }

    @Override
    public List<TaskGrid> findGridPath(String gridId) {
        return taskGridDao.findGridPath(gridId);
    }

    @Override
    public List<TaskGrid> findNearbyGrids(String gridId, Double radius) {
        return taskGridDao.findNearbyGrids(gridId, radius);
    }

    @Override
    public List<TaskGrid> findByLevel(Integer level) {
        return taskGridDao.findByLevel(level);
    }

    @Override
    public List<TaskGrid> findRootGrids() {
        return taskGridDao.findRootGrids();
    }

    // 添加缺少的方法实现
    public List<TaskGrid> findByArea(String area) {
        return taskGridDao.findByArea(area);
    }

    public List<TaskGrid> findGridsByBounds(Double minLng, Double maxLng, Double minLat, Double maxLat) {
        return taskGridDao.findGridsByBounds(minLng, maxLng, minLat, maxLat);
    }

    public void updateStatus(String gridId, Integer status) {
        taskGridDao.updateStatus(gridId, status);
    }

    public void batchUpdateStatus(List<String> gridIds, Integer status) {
        taskGridDao.batchUpdateStatus(gridIds, status);
    }

    public boolean hasChildGrids(String gridId) {
        return taskGridDao.hasChildGrids(gridId);
    }

    public Long countByLevel(int level) {
        return taskGridDao.countByLevel(level);
    }

    @Override
    public List<TaskGrid> buildGridTree(String rootId) {
        // Simplified implementation, returns a flat list of children
        return findChildGrids(rootId);
    }

    @Override
    public List<TaskGrid> getGridPath(String gridId) {
        return taskGridDao.findGridPath(gridId);
    }

    @Override
    public Page<TaskGrid> searchGrids(Page<TaskGrid> page, String keyword) {
        return taskGridDao.findByCondition(page, keyword, keyword, null, 1);
    }

    @Override
    public List<TaskGrid> findNearbyGrids(Double longitude, Double latitude, Double radius) {
        // Simplified implementation
        return taskGridDao.findActiveGrids();
    }

    @Override
    public List<TaskGrid> findByParentId(String parentId) {
        return taskGridDao.findChildGrids(parentId);
    }

    @Override
    public boolean updateGridStatus(String gridId, Integer status, String updatedBy) {
        TaskGrid grid = get(gridId);
        if (grid != null) {
            grid.setStatus(status);
            grid.setUpdatedBy(updatedBy);
            grid.setUpdateTime(new Date());
            save(grid);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public TaskGrid createGrid(TaskGrid grid, String createdBy) {
        grid.setCreatedBy(createdBy);
        grid.setUpdatedBy(createdBy);
        grid.setCreateTime(new Date());
        grid.setUpdateTime(new Date());
        
        if (grid.getStatus() == null) {
            grid.setStatus(1); // 默认激活状态
        }
        
        save(grid);
        return grid;
    }

    @Override
    @Transactional
    public TaskGrid updateGrid(TaskGrid grid, String updatedBy) {
        grid.setUpdatedBy(updatedBy);
        grid.setUpdateTime(new Date());
        save(grid);
        return grid;
    }

    @Override
    public boolean deleteGrid(String gridId, String deletedBy) {
        TaskGrid grid = get(gridId);
        if (grid != null) {
            grid.setStatus(0); // 设置为删除状态
            grid.setUpdatedBy(deletedBy);
            grid.setUpdateTime(new Date());
            save(grid);
            return true;
        }
        return false;
    }

    @Override
    public List<TaskGrid> findGridHierarchy(String rootGridId) {
        return findChildGrids(rootGridId);
    }

    @Override
    public boolean validateGridBounds(Double minLng, Double maxLng, Double minLat, Double maxLat) {
        return minLng != null && maxLng != null && minLat != null && maxLat != null &&
               minLng < maxLng && minLat < maxLat &&
               minLng >= -180 && maxLng <= 180 && minLat >= -90 && maxLat <= 90;
    }
}