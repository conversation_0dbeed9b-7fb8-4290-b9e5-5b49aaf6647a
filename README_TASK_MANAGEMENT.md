# 城市体检数据采集任务管理系统

## 系统概述

基于现有DWSurvey问卷调查系统扩展的城市体检数据采集任务管理系统，实现了任务自动推送、智能列表管理、任务详情展示等核心功能。

## 功能特性

### 3.2.1 核心功能
- **任务自动推送**: 根据用户角色和网格自动推送待办任务，支持任务优先级设置和紧急任务推送
- **任务列表管理**: 展示所有待处理采集任务，提供多维度智能排序（距离、紧急程度、任务类型）
- **任务详情展示**: 详细的任务说明和作业要求，动态表单预览和填报规范，历史数据参考和标准示例

### 3.2.2 技术特性
- **实时任务同步**: 基于WebSocket的实时通信机制
- **智能排序算法**: 支持距离、优先级、时间等多维度排序
- **任务缓存**: 支持离线查看和本地缓存
- **权限管理**: 基于网格和角色的权限控制体系

## 技术架构

### 前端技术栈
- Vue.js 2.6.14
- Element UI 2.15.6
- Webpack 4.47.0
- WebSocket客户端
- localStorage缓存

### 后端技术栈
- Spring Boot (基于现有DWSurvey架构)
- Spring WebSocket
- Spring Scheduling
- MySQL数据库
- Redis缓存

## 部署指南

### 1. 数据库初始化

执行数据库脚本创建任务管理相关表：

```sql
-- 执行数据库脚本
mysql -u username -p database_name < database/task_management_schema.sql
```

### 2. 后端部署

#### 2.1 添加依赖配置

确保Spring Boot项目包含以下依赖：

```xml
<!-- WebSocket支持 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-websocket</artifactId>
</dependency>

<!-- 定时任务支持 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context-support</artifactId>
</dependency>
```

#### 2.2 配置文件更新

在`application.properties`中添加：

```properties
# WebSocket配置
spring.websocket.enabled=true

# 定时任务配置
spring.task.scheduling.enabled=true
spring.task.scheduling.pool.size=10
```

#### 2.3 启动后端服务

```bash
cd DWSurvey
mvn clean install
mvn spring-boot:run
```

### 3. 前端部署

#### 3.1 安装依赖

```bash
cd dwsurvey-vue
npm install
```

#### 3.2 启动开发服务器

```bash
npm run dev
```

#### 3.3 生产环境构建

```bash
npm run build
```

## 文件结构

### 后端文件结构

```
DWSurvey/src/main/java/net/diaowen/dwsurvey/
├── entity/                     # 实体类
│   ├── CityTask.java          # 城市任务实体
│   ├── TaskGrid.java          # 网格区域实体
│   └── UserGridRole.java      # 用户网格角色实体
├── dao/                       # 数据访问层
│   ├── CityTaskDao.java       # 任务数据访问接口
│   ├── TaskGridDao.java       # 网格数据访问接口
│   └── UserGridRoleDao.java   # 用户角色数据访问接口
├── service/                   # 业务逻辑层
│   ├── CityTaskManager.java   # 任务管理服务接口
│   ├── TaskGridManager.java   # 网格管理服务接口
│   ├── UserGridRoleManager.java # 用户角色管理服务接口
│   ├── TaskPushService.java   # 任务推送服务
│   └── TaskScheduleService.java # 任务调度服务
├── service/impl/              # 业务逻辑实现
│   ├── CityTaskManagerImpl.java
│   ├── TaskGridManagerImpl.java
│   └── UserGridRoleManagerImpl.java
├── controller/task/           # 控制器层
│   ├── CityTaskController.java # 任务管理控制器
│   ├── TaskGridController.java # 网格管理控制器
│   └── UserGridRoleController.java # 用户角色管理控制器
├── config/                    # 配置类
│   ├── WebSocketConfig.java   # WebSocket配置
│   └── TaskScheduleConfig.java # 定时任务配置
└── handler/                   # 处理器
    └── TaskWebSocketHandler.java # WebSocket消息处理器
```

### 前端文件结构

```
dwsurvey-vue/src/
├── views/dw-task/             # 任务管理页面
│   ├── TaskManagement.vue    # 任务管理主页面
│   └── TaskDetail.vue        # 任务详情页面
├── api/                       # API接口
│   └── city-task.js          # 任务管理API
├── utils/                     # 工具类
│   ├── websocket.js          # WebSocket工具
│   ├── task-sort.js          # 任务排序算法
│   └── task-cache.js         # 任务缓存工具
├── router/                    # 路由配置
│   └── dw-task-routes.js     # 任务管理路由
└── mock/                      # 模拟数据
    └── task-data.js          # 任务测试数据
```

## API接口文档

### 任务管理接口

#### 获取任务列表
```
GET /api/city-task/list
参数: page, pageSize, status, priority, taskType, gridId, assigneeId
返回: 分页任务列表
```

#### 创建任务
```
POST /api/city-task/create
参数: 任务详细信息
返回: 创建结果
```

#### 更新任务
```
POST /api/city-task/update
参数: 任务ID和更新信息
返回: 更新结果
```

#### 分配任务
```
POST /api/city-task/assign
参数: taskId, assigneeId
返回: 分配结果
```

### 网格管理接口

#### 获取网格列表
```
GET /api/task-grid/list
参数: page, pageSize, parentId, gridType
返回: 分页网格列表
```

#### 获取网格树
```
GET /api/task-grid/tree
返回: 网格树形结构
```

### 用户角色管理接口

#### 获取用户角色
```
GET /api/user-grid-role/user/{userId}
返回: 用户在各网格的角色信息
```

#### 分配用户到网格
```
POST /api/user-grid-role/assign
参数: userId, gridId, roleCode, permissions
返回: 分配结果
```

## WebSocket接口

### 连接地址
```
ws://localhost:8080/ws/task?userId={userId}
```

### 消息格式

#### 客户端发送消息
```json
{
  "type": "SUBSCRIBE_GRID",
  "gridId": "grid_001"
}
```

#### 服务端推送消息
```json
{
  "type": "NEW_TASK_ASSIGNMENT",
  "taskId": "task_001",
  "taskTitle": "社区环境卫生检查",
  "priority": 3,
  "timestamp": 1638360000000
}
```

## 使用指南

### 1. 系统登录

使用DWSurvey现有的用户账号登录系统。

### 2. 网格配置

1. 管理员登录后，进入网格管理页面
2. 创建城市、区县、街道、社区等层级网格
3. 为每个网格分配负责人和数据采集员

### 3. 任务管理

1. 创建任务：填写任务基本信息、选择网格区域、设置优先级
2. 任务分配：系统自动分配或手动指定负责人
3. 任务执行：负责人接收任务推送，查看任务详情，完成数据采集
4. 任务监控：管理员实时监控任务进度和完成情况

### 4. 实时通信

- 系统自动推送新任务通知
- 紧急任务立即推送给相关人员
- 任务状态变更实时同步
- 截止时间提醒自动发送

## 数据库表说明

### 核心表结构

1. **t_task_grid**: 网格区域表，存储城市网格划分信息
2. **t_user_grid_role**: 用户网格角色表，管理用户在不同网格的权限
3. **t_city_task**: 城市体检任务表，存储任务详细信息
4. **t_task_execution_log**: 任务执行记录表，记录任务操作历史
5. **t_task_push_log**: 任务推送记录表，记录消息推送历史
6. **t_task_statistics**: 任务统计表，存储任务统计数据

### 视图说明

1. **v_task_with_grid**: 任务与网格关联视图，简化查询
2. **v_user_task_summary**: 用户任务汇总视图，统计用户任务情况

## 性能优化

### 数据库优化
- 创建复合索引提高查询性能
- 使用视图简化复杂查询
- 定期清理历史数据

### 缓存策略
- 使用Redis缓存热点数据
- 前端localStorage缓存任务列表
- WebSocket连接池管理

### 系统监控
- 定时清理WebSocket连接
- 监控任务执行性能
- 记录系统操作日志

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认WebSocket端口开放
   - 验证用户认证状态

2. **任务推送不及时**
   - 检查定时任务是否正常运行
   - 确认WebSocket连接状态
   - 查看任务调度日志

3. **数据同步异常**
   - 检查数据库连接
   - 确认事务配置
   - 查看错误日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/dwsurvey.log

# 查看WebSocket连接日志
grep "WebSocket" logs/dwsurvey.log

# 查看任务调度日志
grep "TaskSchedule" logs/dwsurvey.log
```

## 系统维护

### 定期维护任务

1. **数据库维护**
   - 定期备份数据库
   - 清理过期日志数据
   - 优化数据库索引

2. **系统监控**
   - 监控系统性能指标
   - 检查WebSocket连接数
   - 统计任务完成情况

3. **安全检查**
   - 更新系统补丁
   - 检查用户权限配置
   - 审计系统操作日志

## 技术支持

如有技术问题，请联系开发团队或查看相关文档：

- 系统架构文档
- API接口文档
- 数据库设计文档
- 部署运维文档

---

**版本**: v1.0.0  
**更新时间**: 2024年12月  
**开发团队**: DWSurvey Team