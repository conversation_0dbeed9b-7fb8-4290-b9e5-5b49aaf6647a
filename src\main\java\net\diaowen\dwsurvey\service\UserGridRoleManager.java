package net.diaowen.dwsurvey.service;

import net.diaowen.common.service.BaseService;
import net.diaowen.dwsurvey.entity.UserGridRole;
import net.diaowen.common.plugs.page.Page;

import java.util.List;

/**
 * 用户网格角色权限管理服务接口
 *
 * <AUTHOR> Team
 */
public interface UserGridRoleManager extends BaseService<UserGridRole, String> {

    Page<UserGridRole> findByCondition(Page<UserGridRole> page, String userId, String gridId, String roleCode, Integer status);

    List<UserGridRole> findByUserId(String userId);

    List<UserGridRole> findByGridCode(String gridCode);

    UserGridRole findByUserIdAndGridCode(String userId, String gridCode);

    List<UserGridRole> findByRoleType(String roleType);

    String getUserPermissions(String userId, String gridCode);

    boolean hasPermission(String userId, String gridCode, String permission);

    UserGridRole assignRole(String userId, String gridCode, String roleType,
                           String permissions, String assignedBy);

    UserGridRole updateRole(String roleId, String roleType, String permissions, String updatedBy);

    boolean removeRole(String roleId, String userId);

    boolean updateRoleStatus(String roleId, Integer status, String updatedBy);

    List<String> getUserManagedGrids(String userId);

    List<UserGridRole> getGridCollectors(String gridCode);

    List<String> findGridIdsByUserId(String userId);

    boolean hasGridPermission(String userId, String gridCode, String permission);

    List<UserGridRole> findByGridId(String gridId);

    List<String> findUserIdsByGridId(String gridId);

    boolean assignUserToGrid(String userId, String gridId, String roleType, String permissions);

    boolean removeUserFromGrid(String userId, String gridId);

    boolean updateUserRole(String userId, String gridId, String roleType);

    boolean updateUserPermissions(String userId, String gridId, String permissions);

    List<UserGridRole> findManagersByGrid(String gridId);

    List<UserGridRole> findCollectorsByGrid(String gridId);

    boolean isGridManager(String userId, String gridId);

    boolean isDataCollector(String userId, String gridId);

    List<String> findManagedGridIds(String userId);

    void batchAssignUsers(List<String> userIds, String gridId, String roleCode, String permissions);

    void batchRemoveUsers(List<String> userIds, String gridId);

    long countByRole(String roleType);
}