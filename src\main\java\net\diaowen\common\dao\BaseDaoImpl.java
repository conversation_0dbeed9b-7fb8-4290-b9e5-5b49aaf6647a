package net.diaowen.common.dao;

import net.diaowen.common.plugs.page.Page;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Projections;
import org.hibernate.query.Query;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.List;

public abstract class BaseDaoImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements BaseDao<T, ID> {

    private final EntityManager entityManager;

    public BaseDaoImpl(Class<T> domainClass, EntityManager em) {
        super(domainClass, em);
        this.entityManager = em;
    }

    protected Session getSession() {
        return entityManager.unwrap(Session.class);
    }

    @SuppressWarnings("unchecked")
    protected List<T> find(String hql, Object... params) {
        Query<T> query = getSession().createQuery(hql, (Class<T>) getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.list();
    }

    @SuppressWarnings("unchecked")
    protected T findUnique(String hql, Object... params) {
        Query<T> query = getSession().createQuery(hql, (Class<T>) getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.uniqueResult();
    }
    
    @SuppressWarnings("unchecked")
    protected Page<T> findPage(Page<T> page, String hql, Object... params) {
        // Count query
        String countHql = "select count(*) " + hql;
        Query<Long> countQuery = getSession().createQuery(countHql, Long.class);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                countQuery.setParameter(i, params[i]);
            }
        }
        Long total = countQuery.uniqueResult();
        page.setTotal(total.intValue());

        // Data query
        Query<T> dataQuery = getSession().createQuery(hql, (Class<T>) getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                dataQuery.setParameter(i, params[i]);
            }
        }
        dataQuery.setFirstResult((page.getPageNo() - 1) * page.getPageSize());
        dataQuery.setMaxResults(page.getPageSize());
        page.setList(dataQuery.list());

        return page;
    }

    protected int executeUpdate(String hql, Object... params) {
        Query<?> query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.executeUpdate();
    }

    @Override
    public Page<T> findByCondition(Page<T> page, List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Get total count
        Number totalCount = (Number) criteria.setProjection(Projections.rowCount()).uniqueResult();
        if (totalCount != null) {
            page.setTotal(totalCount.intValue());
        } else {
            page.setTotal(0);
        }

        // Reset projection and get paginated results
        criteria.setProjection(null);
        criteria.setResultTransformer(Criteria.ROOT_ENTITY);
        criteria.setFirstResult((page.getPageNo() - 1) * page.getPageSize());
        criteria.setMaxResults(page.getPageSize());
        page.setList(criteria.list());
        return page;
    }
}
