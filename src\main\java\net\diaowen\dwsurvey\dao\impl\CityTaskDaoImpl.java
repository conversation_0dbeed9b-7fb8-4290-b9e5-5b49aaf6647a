package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.CityTaskDao;
import net.diaowen.dwsurvey.entity.CityTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.*;

/**
 * 城市体检数据采集任务DAO实现类
 * 
 * <AUTHOR> Team
 */
@Repository
public class CityTaskDaoImpl extends BaseDaoImpl<CityTask, String> implements CityTaskDao {

    @Autowired
    public CityTaskDaoImpl(EntityManager em) {
        super(CityTask.class, em);
    }

    @Override
    public Page<CityTask> findTasksByUser(Page<CityTask> page, String userId, String taskType,
                                          Integer status, Integer priority, String gridCode) {
        String hql = "from CityTask t where 1=1";
        List<Object> params = new ArrayList<>();
        
        if (userId != null && !userId.isEmpty()) {
            hql += " and t.assignedUserId = ?";
            params.add(userId);
        }
        if (taskType != null && !taskType.isEmpty()) {
            hql += " and t.taskType = ?";
            params.add(taskType);
        }
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        if (priority != null) {
            hql += " and t.priority = ?";
            params.add(priority);
        }
        if (gridCode != null && !gridCode.isEmpty()) {
            hql += " and t.gridCode = ?";
            params.add(gridCode);
        }
        
        hql += " order by t.createTime desc";
        
        return findPage(page, hql, params.toArray());
    }

    @Override
    public List<CityTask> findTasksByGrid(String gridCode, Integer status) {
        String hql = "from CityTask t where t.gridCode = ?";
        List<Object> params = new ArrayList<>();
        params.add(gridCode);
        
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        
        hql += " order by t.createTime desc";
        
        return find(hql, params.toArray());
    }

    @Override
    public Map<String, Object> getTaskStatsByUser(String userId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 统计各状态任务数量
        String hql = "select t.status, count(*) from CityTask t where t.assignedUserId = ? group by t.status";
        List<Object[]> results = find(hql, userId);
        
        int totalTasks = 0;
        int pendingTasks = 0;
        int inProgressTasks = 0;
        int completedTasks = 0;
        
        for (Object[] result : results) {
            Integer status = (Integer) result[0];
            Long count = (Long) result[1];
            totalTasks += count.intValue();
            
            switch (status) {
                case 0: pendingTasks = count.intValue(); break;
                case 1: inProgressTasks = count.intValue(); break;
                case 2: completedTasks = count.intValue(); break;
            }
        }
        
        stats.put("totalTasks", totalTasks);
        stats.put("pendingTasks", pendingTasks);
        stats.put("inProgressTasks", inProgressTasks);
        stats.put("completedTasks", completedTasks);
        
        return stats;
    }

    @Override
    public List<CityTask> findNearbyTasks(Double longitude, Double latitude, Double radius, String userId) {
        // 简化实现，实际应使用地理位置计算
        String hql = "from CityTask t where t.assignedUserId = ? and t.status in (0, 1) order by t.createTime desc";
        return find(hql, userId);
    }

    @Override
    public List<CityTask> findTasksNearDeadline(String userId, Integer hours) {
        String hql = "from CityTask t where t.assignedUserId = ? and t.deadline <= ? and t.status in (0, 1) order by t.deadline asc";
        Date deadline = new Date(System.currentTimeMillis() + hours * 60 * 60 * 1000);
        return find(hql, userId, deadline);
    }

    @Override
    public Page<CityTask> searchTasks(Page<CityTask> page, String userId, String keyword) {
        String hql = "from CityTask t where t.assignedUserId = ? and (t.taskTitle like ? or t.taskDescription like ?) order by t.createTime desc";
        String searchKeyword = "%" + keyword + "%";
        return findPage(page, hql, userId, searchKeyword, searchKeyword);
    }

    @Override
    public List<CityTask> findUnassignedTasks() {
        String hql = "from CityTask t where t.assignedUserId is null and t.status = 0 order by t.priority desc, t.createTime asc";
        return find(hql);
    }

    @Override
    public List<CityTask> findOverdueTasks(String userId) {
        String hql = "from CityTask t where t.assignedUserId = ? and t.deadline < ? and t.status in (0, 1) order by t.deadline asc";
        return find(hql, userId, new Date());
    }

    @Override
    public Map<String, Object> generateDailyStatistics(Date date) {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取当天开始和结束时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date startTime = cal.getTime();
        
        cal.add(Calendar.DAY_OF_MONTH, 1);
        Date endTime = cal.getTime();
        
        // 统计当天创建的任务
        String hql = "select count(*) from CityTask t where t.createTime >= ? and t.createTime < ?";
        Long createdCount = (Long) findUnique(hql, startTime, endTime);
        
        // 统计当天完成的任务
        hql = "select count(*) from CityTask t where t.completedTime >= ? and t.completedTime < ?";
        Long completedCount = (Long) findUnique(hql, startTime, endTime);
        
        stats.put("createdTasks", createdCount);
        stats.put("completedTasks", completedCount);
        stats.put("date", date);
        
        return stats;
    }

    @Override
    public int countActiveTasksByAssignee(String userId) {
        String hql = "select count(*) from CityTask t where t.assignedUserId = ? and t.status in (0, 1)";
        Long count = (Long) findUnique(hql, userId);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public List<CityTask> findUrgentTasks(String userId) {
        String hql = "from CityTask t where t.assignedUserId = ? and t.priority = 4 and t.status in (0, 1) order by t.createTime desc";
        return find(hql, userId);
    }

    @Override
    public boolean hasTaskPermission(String userId, String taskId, String permission) {
        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }
        
        // 简化权限检查：创建者和分配者有权限
        return userId.equals(task.getCreatedBy()) || userId.equals(task.getAssignedUserId());
    }

    @Override
    public int batchUpdateStatus(List<String> taskIds, Integer status, String updatedBy) {
        if (taskIds == null || taskIds.isEmpty()) {
            return 0;
        }
        String hql = "update CityTask set status = :status, updatedBy = :updatedBy, updatedTime = :updatedTime where id in (:ids)";
        return getSession().createQuery(hql)
                .setParameter("status", status)
                .setParameter("updatedBy", updatedBy)
                .setParameter("updatedTime", new Date())
                .setParameter("ids", taskIds)
                .executeUpdate();
    }
}
