-- 城市体检数据采集任务管理系统数据库表结构
-- 基于DWSurvey现有数据库结构扩展

-- 1. 网格区域表
CREATE TABLE `t_task_grid` (
  `id` varchar(32) NOT NULL COMMENT '网格ID',
  `grid_code` varchar(50) NOT NULL COMMENT '网格编码',
  `grid_name` varchar(100) NOT NULL COMMENT '网格名称',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父网格ID',
  `grid_level` int(11) NOT NULL DEFAULT '1' COMMENT '网格层级',
  `grid_type` varchar(20) NOT NULL DEFAULT 'COMMUNITY' COMMENT '网格类型：CITY-城市,DISTRICT-区县,STREET-街道,COMMUNITY-社区',
  `area_code` varchar(20) DEFAULT NULL COMMENT '行政区划代码',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `boundary_data` text COMMENT '边界数据(GeoJSON格式)',
  `address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `description` text COMMENT '网格描述',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用,1-启用',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `creator_id` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_grid_code` (`grid_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_grid_level` (`grid_level`),
  KEY `idx_grid_type` (`grid_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网格区域表';

-- 2. 用户网格角色表
CREATE TABLE `t_user_grid_role` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `grid_id` varchar(32) NOT NULL COMMENT '网格ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色代码：GRID_ADMIN-网格管理员,GRID_MANAGER-网格负责人,DATA_COLLECTOR-数据采集员',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `permissions` text COMMENT '权限列表(JSON格式)',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用,1-启用',
  `assign_date` datetime NOT NULL COMMENT '分配时间',
  `expire_date` datetime DEFAULT NULL COMMENT '过期时间',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `creator_id` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_grid_role` (`user_id`,`grid_id`,`role_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_grid_id` (`grid_id`),
  KEY `idx_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户网格角色表';

-- 3. 城市体检任务表
CREATE TABLE `t_city_task` (
  `id` varchar(32) NOT NULL COMMENT '任务ID',
  `task_code` varchar(50) NOT NULL COMMENT '任务编码',
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型：SURVEY-问卷调查,INSPECTION-现场检查,DATA_COLLECTION-数据采集,MONITORING-监测任务',
  `category` varchar(50) DEFAULT NULL COMMENT '任务分类',
  `priority` int(11) NOT NULL DEFAULT '3' COMMENT '优先级：1-低,2-中,3-高,4-紧急',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待处理,ASSIGNED-已分配,IN_PROGRESS-进行中,COMPLETED-已完成,CANCELLED-已取消,OVERDUE-已逾期',
  `grid_id` varchar(32) NOT NULL COMMENT '所属网格ID',
  `survey_id` varchar(32) DEFAULT NULL COMMENT '关联问卷ID',
  `form_data` text COMMENT '表单数据(JSON格式)',
  `assignee_id` varchar(32) DEFAULT NULL COMMENT '负责人ID',
  `creator_id` varchar(32) NOT NULL COMMENT '创建人ID',
  `start_date` datetime DEFAULT NULL COMMENT '开始时间',
  `deadline` datetime DEFAULT NULL COMMENT '截止时间',
  `completed_date` datetime DEFAULT NULL COMMENT '完成时间',
  `location_longitude` decimal(10,7) DEFAULT NULL COMMENT '任务位置经度',
  `location_latitude` decimal(10,7) DEFAULT NULL COMMENT '任务位置纬度',
  `location_address` varchar(200) DEFAULT NULL COMMENT '任务位置地址',
  `estimated_duration` int(11) DEFAULT NULL COMMENT '预计耗时(分钟)',
  `actual_duration` int(11) DEFAULT NULL COMMENT '实际耗时(分钟)',
  `progress` int(11) NOT NULL DEFAULT '0' COMMENT '完成进度(0-100)',
  `result_data` text COMMENT '任务结果数据(JSON格式)',
  `attachments` text COMMENT '附件信息(JSON格式)',
  `remarks` text COMMENT '备注',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签(逗号分隔)',
  `is_urgent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否紧急任务',
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否循环任务',
  `recurring_rule` varchar(200) DEFAULT NULL COMMENT '循环规则',
  `parent_task_id` varchar(32) DEFAULT NULL COMMENT '父任务ID',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `updater_id` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code` (`task_code`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`),
  KEY `idx_grid_id` (`grid_id`),
  KEY `idx_survey_id` (`survey_id`),
  KEY `idx_assignee_id` (`assignee_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_deadline` (`deadline`),
  KEY `idx_create_date` (`create_date`),
  KEY `idx_is_urgent` (`is_urgent`),
  KEY `idx_parent_task_id` (`parent_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='城市体检任务表';

-- 4. 任务执行记录表
CREATE TABLE `t_task_execution_log` (
  `id` varchar(32) NOT NULL COMMENT '记录ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `executor_id` varchar(32) NOT NULL COMMENT '执行人ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型：CREATE-创建,ASSIGN-分配,START-开始,PAUSE-暂停,RESUME-恢复,COMPLETE-完成,CANCEL-取消',
  `old_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(20) DEFAULT NULL COMMENT '新状态',
  `execution_data` text COMMENT '执行数据(JSON格式)',
  `remarks` text COMMENT '备注',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_executor_id` (`executor_id`),
  KEY `idx_action` (`action`),
  KEY `idx_execution_time` (`execution_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行记录表';

-- 5. 任务推送记录表
CREATE TABLE `t_task_push_log` (
  `id` varchar(32) NOT NULL COMMENT '推送记录ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `recipient_id` varchar(32) NOT NULL COMMENT '接收人ID',
  `push_type` varchar(50) NOT NULL COMMENT '推送类型：NEW_TASK-新任务,URGENT_TASK-紧急任务,STATUS_UPDATE-状态更新,REMINDER-提醒,DEADLINE-截止提醒',
  `push_channel` varchar(20) NOT NULL COMMENT '推送渠道：WEBSOCKET-WebSocket,SMS-短信,EMAIL-邮件,APP_PUSH-应用推送',
  `push_content` text COMMENT '推送内容',
  `push_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '推送状态：PENDING-待推送,SUCCESS-成功,FAILED-失败',
  `push_time` datetime NOT NULL COMMENT '推送时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `error_message` text COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_recipient_id` (`recipient_id`),
  KEY `idx_push_type` (`push_type`),
  KEY `idx_push_status` (`push_status`),
  KEY `idx_push_time` (`push_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务推送记录表';

-- 6. 任务统计表
CREATE TABLE `t_task_statistics` (
  `id` varchar(32) NOT NULL COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `grid_id` varchar(32) DEFAULT NULL COMMENT '网格ID(为空表示全局统计)',
  `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID(为空表示网格统计)',
  `task_type` varchar(50) DEFAULT NULL COMMENT '任务类型(为空表示所有类型)',
  `total_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '总任务数',
  `pending_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '待处理任务数',
  `in_progress_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '进行中任务数',
  `completed_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '已完成任务数',
  `overdue_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '逾期任务数',
  `cancelled_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '已取消任务数',
  `urgent_tasks` int(11) NOT NULL DEFAULT '0' COMMENT '紧急任务数',
  `avg_completion_time` decimal(10,2) DEFAULT NULL COMMENT '平均完成时间(小时)',
  `completion_rate` decimal(5,2) DEFAULT NULL COMMENT '完成率(%)',
  `on_time_rate` decimal(5,2) DEFAULT NULL COMMENT '按时完成率(%)',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date_grid_user_type` (`stat_date`,`grid_id`,`user_id`,`task_type`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_grid_id` (`grid_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_type` (`task_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务统计表';

-- 插入初始数据

-- 插入默认网格数据
INSERT INTO `t_task_grid` (`id`, `grid_code`, `grid_name`, `parent_id`, `grid_level`, `grid_type`, `area_code`, `longitude`, `latitude`, `address`, `contact_person`, `contact_phone`, `description`, `status`, `create_date`, `creator_id`) VALUES
('grid_001', 'CITY_001', '示例城市', NULL, 1, 'CITY', '100000', 116.4074, 39.9042, '北京市', '张三', '13800138000', '示例城市网格', 1, NOW(), 'admin'),
('grid_002', 'DISTRICT_001', '示例区县', 'grid_001', 2, 'DISTRICT', '100100', 116.4074, 39.9042, '北京市朝阳区', '李四', '13800138001', '示例区县网格', 1, NOW(), 'admin'),
('grid_003', 'STREET_001', '示例街道', 'grid_002', 3, 'STREET', '100101', 116.4074, 39.9042, '北京市朝阳区建国门街道', '王五', '13800138002', '示例街道网格', 1, NOW(), 'admin'),
('grid_004', 'COMMUNITY_001', '示例社区', 'grid_003', 4, 'COMMUNITY', '100101001', 116.4074, 39.9042, '北京市朝阳区建国门街道示例社区', '赵六', '13800138003', '示例社区网格', 1, NOW(), 'admin');

-- 插入示例任务数据
INSERT INTO `t_city_task` (`id`, `task_code`, `title`, `description`, `task_type`, `category`, `priority`, `status`, `grid_id`, `assignee_id`, `creator_id`, `start_date`, `deadline`, `location_longitude`, `location_latitude`, `location_address`, `estimated_duration`, `progress`, `is_urgent`, `create_date`) VALUES
('task_001', 'TASK_20241201_001', '社区环境卫生检查', '对社区内的环境卫生状况进行全面检查，包括垃圾清理、绿化维护等', 'INSPECTION', '环境卫生', 3, 'PENDING', 'grid_004', NULL, 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 116.4074, 39.9042, '北京市朝阳区建国门街道示例社区', 120, 0, 0, NOW()),
('task_002', 'TASK_20241201_002', '居民满意度调查', '针对社区服务质量进行居民满意度调查', 'SURVEY', '民意调查', 2, 'PENDING', 'grid_004', NULL, 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 14 DAY), 116.4074, 39.9042, '北京市朝阳区建国门街道示例社区', 180, 0, 0, NOW()),
('task_003', 'TASK_20241201_003', '基础设施安全检查', '对社区内基础设施进行安全隐患排查', 'INSPECTION', '安全检查', 4, 'PENDING', 'grid_004', NULL, 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 3 DAY), 116.4074, 39.9042, '北京市朝阳区建国门街道示例社区', 240, 0, 1, NOW());

-- 创建索引优化查询性能
CREATE INDEX `idx_task_grid_status_priority` ON `t_city_task` (`grid_id`, `status`, `priority`);
CREATE INDEX `idx_task_assignee_status_deadline` ON `t_city_task` (`assignee_id`, `status`, `deadline`);
CREATE INDEX `idx_task_type_status_create_date` ON `t_city_task` (`task_type`, `status`, `create_date`);
CREATE INDEX `idx_user_grid_role_status` ON `t_user_grid_role` (`user_id`, `grid_id`, `status`);
CREATE INDEX `idx_grid_parent_level_status` ON `t_task_grid` (`parent_id`, `grid_level`, `status`);

-- 创建视图简化查询
CREATE VIEW `v_task_with_grid` AS
SELECT 
    t.id,
    t.task_code,
    t.title,
    t.description,
    t.task_type,
    t.priority,
    t.status,
    t.assignee_id,
    t.creator_id,
    t.deadline,
    t.create_date,
    t.is_urgent,
    g.grid_name,
    g.grid_code,
    g.grid_type,
    g.address as grid_address
FROM t_city_task t
LEFT JOIN t_task_grid g ON t.grid_id = g.id
WHERE t.status != 'CANCELLED';

CREATE VIEW `v_user_task_summary` AS
SELECT 
    ugr.user_id,
    ugr.grid_id,
    g.grid_name,
    COUNT(t.id) as total_tasks,
    SUM(CASE WHEN t.status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN t.status = 'IN_PROGRESS' THEN 1 ELSE 0 END) as in_progress_tasks,
    SUM(CASE WHEN t.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN t.status = 'OVERDUE' THEN 1 ELSE 0 END) as overdue_tasks,
    SUM(CASE WHEN t.is_urgent = 1 THEN 1 ELSE 0 END) as urgent_tasks
FROM t_user_grid_role ugr
LEFT JOIN t_task_grid g ON ugr.grid_id = g.id
LEFT JOIN t_city_task t ON ugr.grid_id = t.grid_id AND ugr.user_id = t.assignee_id
WHERE ugr.status = 1
GROUP BY ugr.user_id, ugr.grid_id, g.grid_name;