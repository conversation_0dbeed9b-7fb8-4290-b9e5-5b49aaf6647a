package net.diaowen.dwsurvey.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.dwsurvey.service.TaskPushService;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务WebSocket处理器
 * 处理WebSocket连接、消息和断开连接
 * <AUTHOR> Team
 */
@Component
public class TaskWebSocketHandler implements WebSocketHandler {

    @Autowired
    private TaskPushService taskPushService;
    
    @Autowired
    private AccountManager accountManager;
    
    @Autowired
    private UserGridRoleManager userGridRoleManager;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储会话与用户的映射关系
    private final Map<String, String> sessionUserMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        System.out.println("WebSocket连接建立: " + session.getId());
        
        // 从会话中获取用户信息
        String userId = getUserIdFromSession(session);
        if (userId != null) {
            sessionUserMap.put(session.getId(), userId);
            
            // 将用户会话添加到推送服务
            taskPushService.addUserSession(userId, session);
            
            // 获取用户负责的网格，并添加到网格会话
            List<String> gridIds = userGridRoleManager.findGridIdsByUserId(userId);
            for (String gridId : gridIds) {
                taskPushService.addGridSession(gridId, session);
            }
            
            // 发送连接成功消息
            sendMessage(session, "CONNECTION_SUCCESS", "WebSocket连接成功");
            
            System.out.println("用户 " + userId + " WebSocket连接成功");
        } else {
            // 用户未认证，关闭连接
            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("用户未认证"));
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String userId = sessionUserMap.get(session.getId());
        if (userId == null) {
            return;
        }

        try {
            String payload = message.getPayload().toString();
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            
            String messageType = (String) messageData.get("type");
            
            switch (messageType) {
                case "PING":
                    // 心跳检测
                    sendMessage(session, "PONG", "心跳响应");
                    break;
                    
                case "SUBSCRIBE_GRID":
                    // 订阅网格消息
                    String gridId = (String) messageData.get("gridId");
                    if (gridId != null && userGridRoleManager.hasPermission(userId, gridId, "VIEW_TASKS")) {
                        taskPushService.addGridSession(gridId, session);
                        sendMessage(session, "SUBSCRIBE_SUCCESS", "订阅网格 " + gridId + " 成功");
                    } else {
                        sendMessage(session, "SUBSCRIBE_FAILED", "订阅网格失败：权限不足");
                    }
                    break;
                    
                case "UNSUBSCRIBE_GRID":
                    // 取消订阅网格消息
                    String unsubGridId = (String) messageData.get("gridId");
                    if (unsubGridId != null) {
                        taskPushService.removeGridSession(unsubGridId, session);
                        sendMessage(session, "UNSUBSCRIBE_SUCCESS", "取消订阅网格 " + unsubGridId + " 成功");
                    }
                    break;
                    
                case "GET_ONLINE_STATUS":
                    // 获取在线状态
                    Map<String, Object> statusData = new ConcurrentHashMap<>();
                    statusData.put("onlineUsers", taskPushService.getOnlineUserCount());
                    statusData.put("activeGrids", taskPushService.getActiveGridCount());
                    sendMessage(session, "ONLINE_STATUS", statusData);
                    break;
                    
                default:
                    sendMessage(session, "UNKNOWN_MESSAGE", "未知消息类型: " + messageType);
                    break;
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            sendMessage(session, "ERROR", "消息处理失败: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("WebSocket传输错误: " + session.getId());
        exception.printStackTrace();
        
        String userId = sessionUserMap.get(session.getId());
        if (userId != null) {
            cleanupSession(session, userId);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        System.out.println("WebSocket连接关闭: " + session.getId() + ", 状态: " + closeStatus);
        
        String userId = sessionUserMap.get(session.getId());
        if (userId != null) {
            cleanupSession(session, userId);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 从会话中获取用户ID
     */
    private String getUserIdFromSession(WebSocketSession session) {
        try {
            // 从会话属性中获取用户信息
            Object userObj = session.getAttributes().get("user");
            if (userObj instanceof User) {
                return ((User) userObj).getId();
            }
            
            // 从URI参数中获取用户ID（备用方案）
            String query = session.getUri().getQuery();
            if (query != null && query.contains("userId=")) {
                String[] params = query.split("&");
                for (String param : params) {
                    if (param.startsWith("userId=")) {
                        return param.substring(7);
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发送消息到客户端
     */
    private void sendMessage(WebSocketSession session, String type, Object data) {
        if (session.isOpen()) {
            try {
                Map<String, Object> message = new ConcurrentHashMap<>();
                message.put("type", type);
                message.put("data", data);
                message.put("timestamp", System.currentTimeMillis());
                
                String jsonMessage = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(jsonMessage));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 清理会话
     */
    private void cleanupSession(WebSocketSession session, String userId) {
        // 从用户会话中移除
        taskPushService.removeUserSession(userId, session);
        
        // 从所有网格会话中移除
        List<String> gridIds = userGridRoleManager.findGridIdsByUserId(userId);
        for (String gridId : gridIds) {
            taskPushService.removeGridSession(gridId, session);
        }
        
        // 从映射中移除
        sessionUserMap.remove(session.getId());
        
        System.out.println("用户 " + userId + " WebSocket会话清理完成");
    }

    /**
     * 定期清理已关闭的会话
     */
    public void cleanupClosedSessions() {
        taskPushService.cleanupClosedSessions();
        
        // 清理会话映射
        sessionUserMap.entrySet().removeIf(entry -> {
            // 这里可以添加更复杂的清理逻辑
            return false;
        });
    }
}