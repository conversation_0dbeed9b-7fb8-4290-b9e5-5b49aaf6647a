package net.diaowen.common.base.dao;

import net.diaowen.common.plugs.page.Page;
import org.hibernate.criterion.Criterion;
import java.io.Serializable;
import java.util.List;

/**
 * 通用数据访问对象接口
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public interface BaseDao<T, ID extends Serializable> {

    void save(T entity);

    void update(T entity);

    void delete(T entity);

    T get(ID id);

    List<T> findAll();

    List<T> find(String hql, Object... params);

    T findUnique(String hql, Object... params);

    Page<T> findPage(Page<T> page, String hql, Object... params);

    Page<T> findByCondition(Page<T> page, List<Criterion> criterions);
}