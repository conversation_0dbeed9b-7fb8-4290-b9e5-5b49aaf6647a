package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.common.plugs.page.Page;

import java.util.List;
import java.util.Map;

/**
 * 城市体检数据采集任务DAO接口
 * 
 * <AUTHOR> Team
 */
public interface CityTaskDao extends BaseDao<CityTask, String> {

    /**
     * 根据用户ID分页查询任务列表
     * @param page 分页对象
     * @param userId 用户ID
     * @param taskType 任务类型
     * @param status 任务状态
     * @param priority 优先级
     * @param gridCode 网格编码
     * @return 分页结果
     */
    Page<CityTask> findTasksByUser(Page<CityTask> page, String userId, String taskType, 
                                   Integer status, Integer priority, String gridCode);

    /**
     * 根据网格编码查询任务列表
     * @param gridCode 网格编码
     * @param status 任务状态
     * @return 任务列表
     */
    List<CityTask> findTasksByGrid(String gridCode, Integer status);

    /**
     * 根据用户ID统计任务数量
     * @param userId 用户ID
     * @return 统计结果Map
     */
    Map<String, Object> getTaskStatsByUser(String userId);

    /**
     * 根据位置坐标查询附近的任务
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 半径(公里)
     * @param userId 用户ID
     * @return 任务列表
     */
    List<CityTask> findNearbyTasks(Double longitude, Double latitude, Double radius, String userId);

    /**
     * 查询即将到期的任务
     * @param userId 用户ID
     * @param hours 小时数
     * @return 任务列表
     */
    List<CityTask> findTasksNearDeadline(String userId, Integer hours);

    /**
     * 根据关键词搜索任务
     * @param page 分页对象
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 分页结果
     */
    Page<CityTask> searchTasks(Page<CityTask> page, String userId, String keyword);

    /**
     * 批量更新任务状态
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @param updatedBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(List<String> taskIds, Integer status, String updatedBy);

    /**
     * 查找未分配的任务
     * @return 未分配任务列表
     */
    List<CityTask> findUnassignedTasks();

    /**
     * 查找逾期任务
     * @param userId 用户ID
     * @return 逾期任务列表
     */
    List<CityTask> findOverdueTasks(String userId);

    /**
     * 生成每日统计
     * @param date 日期
     * @return 统计结果
     */
    Map<String, Object> generateDailyStatistics(java.util.Date date);

    /**
     * 统计用户活跃任务数量
     * @param userId 用户ID
     * @return 任务数量
     */
    int countActiveTasksByAssignee(String userId);

    /**
     * 查找紧急任务
     * @param userId 用户ID
     * @return 紧急任务列表
     */
    List<CityTask> findUrgentTasks(String userId);

    /**
     * 检查任务权限
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param permission 权限类型
     * @return 是否有权限
     */
    boolean hasTaskPermission(String userId, String taskId, String permission);
}
