(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{"/N7E":function(e,t,s){},"0Snz":function(e,t,s){"use strict";s.r(t);var r=s("Dr/0"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("LbtC"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("EUav")},"data-v-154202b4",null);t.default=l.exports},"5vCQ":function(e,t,s){},"8MAP":function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dwSurveyAnswerListV6=function(e){return(0,n.default)({url:"/api/dwsurvey/app/v6/dw-answer-data-survey/list.do",method:"get",params:e})},t.dwSurveyAnswerStatsV6=function(e){return(0,n.default)({url:"/api/dwsurvey/app/v6/dw-answer-data-survey/survey-stats.do",method:"get",params:e})},t.dwSurveyAnswerExportSync=function(e){return(0,n.default)({url:"/api/dwsurvey/app/v6/dw-answer-data-survey/export-by-sync.do",method:"get",params:e})},t.dwSurveyAnswerExportLogInfo=function(e){return(0,n.default)({url:"/api/dwsurvey/app/v6/answer/export-log/export-log-info.do",method:"get",params:e})};var r,n=(r=s("t3Un"))&&r.__esModule?r:{default:r}},"91RP":function(e,t,s){"use strict";s.r(t);var r=s("gA2T"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("X1wL"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("mftc")},"data-v-7efee448",null);t.default=l.exports},"9q9F":function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,n=(r=s("Y/Yz"))&&r.__esModule?r:{default:r};t.default={name:"DwSurveyAnswerInfoDialog",components:{DwSurveyAnswerReview:n.default},data:function(){return{padPhoneAnBodySpan:{xs:{span:24,offset:0},sm:{span:24,offset:0},md:{span:24,offset:0},lg:{span:24,offset:0},xl:{span:24,offset:0}},dialogFormVisible:!1,row:null}},methods:{openDialog:function(e){this.dialogFormVisible=!0,this.row=e,console.debug("row",e)},closeDialog:function(){this.row=null}}}},"C/hQ":function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=s("GUUC"),n=s("GUUC");t.default={name:"DwSurveyDcsWrapper",props:{id:{type:String,default:""},isAnswerUrl:{type:Boolean,default:!1},isSurveySet:{type:Boolean,default:!1},isSiteShare:{type:Boolean,default:!1},isSiteComp:{type:Boolean,default:!1},isAnswerWx:{type:Boolean,default:!1},isSurveyChart:{type:Boolean,default:!1},isAnswerData:{type:Boolean,default:!1},isSurveyLog:{type:Boolean,default:!1},isAnswerLog:{type:Boolean,default:!1},isAnswerUrlV6:{type:Boolean,default:!1}},data:function(){return{survey:{sid:"",answerUrl:"",answerUrl1:"",answerUrlQR:"",siteCompCodeRoot:"",surveyState:""},prevPath:"/dw"}},mounted:function(){console.debug(Object({NODE_ENV:"production",DW_API_URL:"",DW_WEB_URL:"",DW_RESOURCE_URL:""})),this.getSurveyInfo()},methods:{buttonClickA:function(e){window.location.href=e},handlePush:function(e){this.$router.push(e)},surveyStateChange:function(){var e=this;console.debug(this.survey.surveyState),(0,n.dwSurveyUpState)(this.$route.params.id,this.survey.surveyState).then(function(t){200===t.data.resultCode?e.$message.success("问卷状态设置成功"):e.$message.error("问卷状态设置失败")})},getSurveyInfo:function(){var e=this;(0,r.dwSurveyInfo)(this.$route.params.id).then(function(t){var s=t.data.data;e.survey=s,e.survey.answerUrl=location.origin+"/#/diaowen/"+e.survey.sid,e.survey.answerUrl1=location.origin+"/static/diaowen/answer-p.html?sid="+e.survey.sid,e.survey.answerUrlQR="/api/dwsurvey/anon/response/answerTD.do?surveyId="+e.survey.id,e.survey.siteCompCodeRoot='<div id="dwsurveyWebAnswerCompCode"><div id="dwsurveyWebSiteFixed" style="position: fixed; right: 0px; left: auto; top: 520px; z-index: 99999;"><a target=\'_blank\' id="dwsurveyWebSiteFixedA" href="'+e.survey.answerUrl+'" style="background-color: rgb(24, 144, 255); width: 15px; display: block; padding: 10px 6px 10px 10px; color: white; cursor: pointer; float: right; vertical-align: middle; text-decoration: none; font-size: 12px; box-sizing: content-box; line-height: 20px;">问卷调查</a></div></div>',e.survey.surveyDetail.effective=1===s.surveyDetail.effective,e.survey.surveyDetail.effectiveIp=1===s.surveyDetail.effectiveIp,e.survey.surveyDetail.refresh=1===s.surveyDetail.refresh,e.survey.surveyDetail.rule=1===s.surveyDetail.rule,e.survey.surveyDetail.ynEndNum=1===s.surveyDetail.ynEndNum,e.survey.surveyDetail.ynEndTime=1===s.surveyDetail.ynEndTime,e.survey.answerUrlV6=location.origin+"/#/v6/diaowen/an/"+e.survey.sid})}}}},CxC7:function(e,t,s){"use strict";s.r(t);var r=s("KKXl"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("up8O"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("tqcQ")},"data-v-4cea0368",null);t.default=l.exports},"Dr/0":function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(s("s7of")),n=a(s("sxGJ"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwAnswerWeixin",components:{DwSurveyDcsWrapper:r.default,Clipboard:n.default},data:function(){return{code:"http://ent.surveyform.cn/#/diaowen/wdhl2uv6e9x",src:"https://ent.surveyform.cn/api/dwsurvey/anon/response/answerTD.do?surveyId=465d3020-0880-4c37-ab09-cc40869758de"}},methods:{copyActiveCode:function(e,t){var s=this,r=new n.default(e.target,{text:function(){return t}});r.on("success",function(e){s.$message({type:"success",message:"复制成功"}),r.off("error"),r.off("success"),r.destroy()}),r.on("error",function(e){s.$message({type:"waning",message:"该浏览器不支持自动复制"}),r.off("error"),r.off("success"),r.destroy()}),r.onClick(e)}}}},EEXx:function(e,t,s){},EUav:function(e,t,s){},G5SN:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(s("sxGJ")),n=a(s("fcY9"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwSiteComp",components:{DwSurveyDcsWrapperV6:n.default,Clipboard:r.default},data:function(){return{color1:"#409EFF",color2:"#FFFFFF",value:60,radio:"右边",siteCompCode:'<div id="dwsurveyWebSiteFixed" style="position: fixed; right: 0px; left: auto; top: 520px; z-index: 99999;"><a target=\'_blank\' id="dwsurveyWebSiteFixedA" href="" style="background-color: rgb(24, 144, 255); width: 15px; display: block; padding: 10px 6px 10px 10px; color: white; cursor: pointer; float: right; vertical-align: middle; text-decoration: none; font-size: 12px; box-sizing: content-box; line-height: 20px;">问卷调查</a></div>'}},methods:{copyActiveCode:function(e,t){var s=this,n=document.getElementById("dwsurveyWebAnswerCompCode");this.siteCompCode=n.innerHTML;var a=new r.default(e.target,{text:function(){return s.siteCompCode}});a.on("success",function(e){s.$message({type:"success",message:"复制成功"}),a.off("error"),a.off("success"),a.destroy()}),a.on("error",function(e){s.$message({type:"waning",message:"该浏览器不支持自动复制"}),a.off("error"),a.off("success"),a.destroy()}),a.onClick(e)},handleSlider:function(e){var t=window.innerHeight-10*e;t<0&&(t=0);var s=document.getElementById("dwsurveyWebAnswerCompCode"),r=document.getElementById("dwsurveyWebSiteFixed");null!==r&&(r.style.top=t+"px",console.debug(s.innerHTML),this.siteCompCode=s.innerHTML)},handleLRButton:function(e){console.debug(e);var t=document.getElementById("dwsurveyWebAnswerCompCode"),s=document.getElementById("dwsurveyWebSiteFixed");null!==s&&("左边"===e?(s.style.left="0px",s.style.right="auto"):(s.style.left="auto",s.style.right="0px"),console.debug(t.innerHTML),this.siteCompCode=t.innerHTML)},handleBgColor:function(e){var t=document.getElementById("dwsurveyWebAnswerCompCode"),s=document.getElementById("dwsurveyWebSiteFixedA");null!==s&&(s.style.backgroundColor=e,console.debug(t.innerHTML),this.siteCompCode=t.innerHTML)},handleTextColor:function(e){var t=document.getElementById("dwsurveyWebAnswerCompCode"),s=document.getElementById("dwsurveyWebSiteFixedA");s.hasAttribute("style")&&(s.style.color=e,console.debug(t.innerHTML),this.siteCompCode=t.innerHTML)}}}},GUUC:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dwSurveyList=function(e,t,s,a){var i={pageSize:e,current:t,surveyName:s,surveyState:a};return(0,r.default)({url:n.default.surveyList,method:"get",params:i})},t.dwSurveyCreate=function(e){return(0,r.default)({url:n.default.surveyCreate,method:"post",data:e})},t.dwSurveyUpState=function(e,t){var s={surveyId:e,surveyState:t};return(0,r.default)({url:n.default.surveyUpState,method:"post",params:s})},t.dwSurveyCopy=function(e,t){var s={fromSurveyId:e,surveyName:t,tag:"2"};return(0,r.default)({url:n.default.surveyCopy,method:"post",params:s})},t.dwSurveyInfo=function(e){var t={id:e};return(0,r.default)({url:n.default.surveyInfo,method:"get",params:t})},t.dwSurveyUpdate=function(e){return(0,r.default)({url:n.default.surveyUpdate,method:"put",data:e})},t.dwSurveyDelete=function(e){return(0,r.default)({url:n.default.surveyDelete,method:"delete",data:e})},t.dwSurveyReport=function(e){var t={surveyId:e};return(0,r.default)({url:n.default.surveyReport,method:"get",params:t})},t.dwSurveyAnswerList=function(e,t,s){var a={pageSize:e,current:t,surveyId:s};return(0,r.default)({url:n.default.surveyAnswerList,method:"get",params:a})},t.dwSurveyAnswerDelete=function(e){return(0,r.default)({url:n.default.surveyAnswerDelete,method:"delete",data:e})},t.dwSurveyAnswerInfo=function(e){var t={answerId:e};return(0,r.default)({url:n.default.surveyAnswerInfo,method:"get",params:t})},t.dwSurveyAnswerDeleteByEs=function(e){return(0,r.default)({url:"/api/dwsurvey/app/v6/dw-answer-data-survey/delete-answer.do",method:"delete",data:e})};var r=a(s("t3Un")),n=a(s("Nlzp"));function a(e){return e&&e.__esModule?e:{default:e}}},GpLw:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("dw-survey-dcs-wrapper-v6",{attrs:{"is-site-comp":!0},scopedSlots:e._u([{key:"dw-dcs-main-slot",fn:function(t){var r=t.survey;return[s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("通过网站挂件快速收集问卷")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("复制右边生成的挂件代码，放入网站Footer页中即可实现全站带有答卷挂件。")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"})]),e._v(" "),s("div",[s("el-row",[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"site-comp-left"},[s("div",{staticClass:"site-comp-left-title"},[e._v("风格设置")]),e._v(" "),s("div",[s("el-row",[s("el-col",{attrs:{span:16}},[s("div",{staticClass:"dw-c-from-item"},[e._v("挂件位置：\n                      "),s("el-radio-group",{attrs:{size:"small"},on:{change:function(t){return e.handleLRButton(t)}},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[s("el-radio-button",{attrs:{label:"左边"}}),e._v(" "),s("el-radio-button",{attrs:{label:"右边"}})],1)],1),e._v(" "),s("div",{staticClass:"dw-c-from-item dw-c-from-item-color"},[s("span",[e._v("背景颜色：")]),e._v(" "),s("el-color-picker",{on:{"active-change":function(t){return e.handleBgColor(t)}},model:{value:e.color1,callback:function(t){e.color1=t},expression:"color1"}})],1),e._v(" "),s("div",{staticClass:"dw-c-from-item dw-c-from-item-color"},[s("span",[e._v("文字颜色：")]),e._v(" "),s("el-color-picker",{on:{"active-change":function(t){return e.handleTextColor(t)}},model:{value:e.color2,callback:function(t){e.color2=t},expression:"color2"}})],1)]),e._v(" "),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-c-from-item"},[s("div",[e._v("挂件高度：")]),e._v(" "),s("div",{staticStyle:{"margin-top":"15px"}},[s("el-slider",{attrs:{vertical:"",height:"200px"},on:{input:e.handleSlider},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),e._v(" "),s("div",{staticStyle:{"padding-top":"15px"}},[s("el-input-number",{attrs:{min:1,max:100,"controls-position":"right",size:"small"},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)])])],1)],1)])]),e._v(" "),s("el-col",{staticStyle:{"text-align":"center",color:"rgb(211, 211, 211)"},attrs:{span:2}},[s("i",{staticClass:"el-icon-right"})]),e._v(" "),s("el-col",{attrs:{span:14}},[s("div",{staticClass:"site-comp-right"},[s("div",[s("el-input",{attrs:{autosize:{minRows:2,maxRows:8},type:"textarea",placeholder:"请输入内容",disabled:""},model:{value:e.siteCompCode,callback:function(t){e.siteCompCode=t},expression:"siteCompCode"}})],1),e._v(" "),s("div",{staticStyle:{"padding-top":"10px"}},[s("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",icon:"el-icon-copy-document"},on:{click:function(t){return e.copyActiveCode(t,e.siteCompCode)}}},[e._v("复制挂件代码")])],1)])])],1),e._v(" "),s("div",{domProps:{innerHTML:e._s(r.siteCompCodeRoot)}})],1)]}}])})],1)},n=[]},KKXl:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(s("sxGJ")),n=a(s("fcY9"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwAnswerUrlV6",components:{DwSurveyDcsWrapperV6:n.default,Clipboard:r.default},props:{survey:{type:Object,default:function(){}}},methods:{downloadAnswerImg:function(e){console.debug(e),window.location.href=e},copyActiveCode:function(e,t){var s=this;console.debug(this.survey);var n=new r.default(e.target,{text:function(){return t}});n.on("success",function(e){s.$message({type:"success",message:"复制成功"}),n.off("error"),n.off("success"),n.destroy()}),n.on("error",function(e){s.$message({type:"warning",message:"该浏览器不支持自动复制"}),n.off("error"),n.off("success"),n.destroy()}),n.onClick(e)}}}},LbtC:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("dw-survey-dcs-wrapper",{attrs:{"is-answer-wx":"true"},scopedSlots:e._u([{key:"dw-dcs-main-slot",fn:function(t){var r=t.survey;return[s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("通过微信二维码发送或分享给好友")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("通过手机扫一扫，或下载二维码，发送或分享给好友，即可进行问卷数据收集。")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"},[s("el-row",{attrs:{type:"flex",justify:"start",align:"middle"}},[s("el-col",{attrs:{span:4}},[s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:r.answerUrlQR}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                  加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1),e._v(" "),s("el-col",{attrs:{span:20}},[s("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"}},[e._v("下载二维码")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p",staticStyle:{padding:"10px"}},[e._v("通过手机扫一扫，或下载二维码，即可进行问卷数据收集。")])],1)],1)],1)]),e._v(" "),s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("微信公众号分享")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("通过微信公众号，发送或分享给被访者。")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"},[s("div",{staticStyle:{"background-color":"#F1F1F1",padding:"10px"}},[s("el-row",{attrs:{type:"flex",gutter:"20",justify:"start",align:"middle"}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-dcs-main-wx-step"},[e._v("1、使用微信扫一扫功能。")]),e._v(" "),s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:"/static/diaowen/images/img1/weixin_collect1.jpg"}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1),e._v(" "),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-dcs-main-wx-step"},[e._v("2、打开问卷后，点击右上角的“分享”按钮。")]),e._v(" "),s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:"/static/diaowen/images/img1/weixin_collect2.jpg"}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1),e._v(" "),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-dcs-main-wx-step"},[e._v("3、选择“发送给朋友”或“分享到朋友圈”")]),e._v(" "),s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:"/static/diaowen/images/img1/weixin_collect3.jpg"}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1)],1)],1)])]),e._v(" "),s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("通过微信公众号")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("复制下面的问卷链接到QQ，Email等工具中直接发给被用户")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"},[s("div",[s("el-input",{staticStyle:{width:"400px"},attrs:{id:"copyCodeInput",readonly:""},model:{value:r.answerUrl,callback:function(t){e.$set(r,"answerUrl",t)},expression:"survey.answerUrl"}}),e._v(" "),s("el-button-group",[s("el-button",{attrs:{type:"primary",icon:"el-icon-copy-document"},on:{click:function(t){return e.copyActiveCode(t,r.answerUrl)}}},[e._v("复制地址")])],1)],1),e._v(" "),s("div",{staticStyle:{padding:"10px","background-color":"#F1F1F1","margin-top":"20px"}},[s("el-row",{attrs:{type:"flex",gutter:"20",justify:"start",align:"top"}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-dcs-main-wx-step"},[e._v("1、打开公众账号。")]),e._v(" "),s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:"/static/diaowen/images/img1/weixin_b_s3.png"}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1),e._v(" "),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"dw-dcs-main-wx-step"},[e._v("2、将链接放入公众号中确认后推送文章。")]),e._v(" "),s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:"/static/diaowen/images/img1/weixin_b_s4.jpeg"}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1)],1)],1)])])]}}])})],1)},n=[]},MIWS:function(e,t,s){},TGtZ:function(e,t,s){},VGgk:function(e,t,s){},W4Fm:function(e,t,s){"use strict";s.r(t);var r=s("G5SN"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("GpLw"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("VGgk")},"data-v-133ff0c4",null);t.default=l.exports},X1wL:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("dw-survey-dcs-wrapper-v6",{attrs:{"is-answer-data":!0},scopedSlots:e._u([{key:"dw-dcs-main-slot",fn:function(t){return t.survey,[null!==e.thSurvey?s("div",[s("dw-survey-answer-data-list",{attrs:{survey:e.thSurvey}})],1):e._e()]}}])})],1)},n=[]},Xkvr:function(e,t,s){"use strict";s.r(t);var r=s("Yzep"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("kiRw"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("MIWS")},"data-v-740f0eff",null);t.default=l.exports},"Y/Yz":function(e,t,s){"use strict";s.r(t);var r=s("eNti"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("vk/N"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("/N7E")},"data-v-92041834",null);t.default=l.exports},Yzep:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,n=s("GUUC"),a=s("8MAP"),i=(r=s("mfmh"))&&r.__esModule?r:{default:r},o=s("+1tm");t.default={name:"DwSurveyAnswerDataList",components:{DwSurveyAnswerInfoDialog:i.default},props:{survey:{type:Object,default:function(){}}},data:function(){return{tableData:[],pageSize:10,currentPage:1,total:0,dialogFormVisible:!1,expUpQu:0,formInline:{ip:null,city:null,anTime:null,handleState:100},handleState:100,threadMax:2e3,expDataContent:1,formInlineDivStyle:"display: flex;",isProgress:!1,percentage:0,customColor:"#1989fa",customColors:[{color:"#cde2f6",percentage:0},{color:"#c1dbf5",percentage:10},{color:"#a7cdf3",percentage:20},{color:"#8bbff3",percentage:40},{color:"#6eb2f6",percentage:60},{color:"#4ca2fa",percentage:80},{color:"#1989fa",percentage:100}],exportLogId:null,exportLogInfoInterval:null}},mounted:function(){this.queryList(1)},methods:{secondsToHms:o.secondsToHms,handleGo:function(e){this.$router.push(e)},handlePush:function(e){this.$router.push(e)},handleDelete:function(e,t){var s=this;console.log(e,t),this.$msgbox.confirm("确认删除此条答卷吗？","删除警告",{type:"warning",confirmButtonText:"确认删除"}).then(function(){var e={id:[t.esId]};(0,n.dwSurveyAnswerDeleteByEs)(e).then(function(e){if(console.log(e),200===e.data.resultCode){s.$message.success("删除成功，即将刷新数据。");var t=s;setTimeout(function(){t.queryList(1)},2e3)}else s.$message.error("删除答卷失败")})}).catch(function(){})},handleCurrentChange:function(e){this.queryList(e)},queryList:function(e){var t=this;this.currentPage=e,(e-1)*this.pageSize>=1e4&&(this.$message({message:"提示：一次查询只展示前1万条数据，如果要查看更多可以修改查询条件，请缩小查询范围！",type:"warning",duration:1e4}),this.currentPage=1e4/this.pageSize-1),console.debug("anTime:",this.formInline.anTime),console.debug("anIp:",this.formInline.ip),console.debug("anCity:",this.formInline.city);var s={bgAnDate:null,endAnDate:null},r=this.formInline.anTime;null!==r&&(s.bgAnDate=r[0],s.endAnDate=r[1]);var n={surveyId:this.$route.params.dwSurveyId,pageNo:this.currentPage,bgAnDate:s.bgAnDate,endAnDate:s.endAnDate,ip:this.formInline.ip,city:this.formInline.city};(0,a.dwSurveyAnswerListV6)(n).then(function(e){var s=e.data;t.pageSize=s.pageSize,t.currentPage=s.pageNo,t.total=s.totalItems,t.tableData=s.result,console.debug("dwSurveyAnswerListV6",e)})},resetSearch:function(){this.formInline={ip:null,city:null,anTime:null},this.queryList(1)},answerView:function(e){this.$refs.dwAnswerInfoDialog.openDialog(e)},handleExport:function(){this.dialogFormVisible=!0,this.percentage=0,this.exportLogId=null,this.isProgress=!1},executeExportData:function(){var e=this;this.isProgress=!0;var t={surveyId:this.$route.params.dwSurveyId,expUpQu:this.expUpQu,handleState:this.handleState,threadMax:this.threadMax,expDataContent:this.expDataContent};(0,a.dwSurveyAnswerExportSync)(t).then(function(t){console.log("dwSurveyAnswerExportSync",t);var s=t.data;if(200===s.resultCode){var r=s.data;e.exportLogId=r.id,console.log("this.exportLogId",e.exportLogId),e.percentage=0,e.exportProgress()}else e.$message.error("导出出错"+s.resultMsg)})},exportProgress:function(){null!==this.exportLogId&&(this.exportLogInfoInterval=setInterval(this.upExportProgress,500))},upExportProgress:function(){var e=this,t={id:this.exportLogId};(0,a.dwSurveyAnswerExportLogInfo)(t).then(function(t){console.log("dwSurveyAnswerExportLogInfo",t);var s=t.data,r=!0;if(200===s.resultCode){var n=s.data;if(null!==n){console.log("progress",n.progress);var a=(100*parseFloat(n.progress)).toFixed(2);console.log("this progress",a),e.percentage=parseInt(a),a<=1&&(e.percentage=1),console.log("this percentage",e.percentage),r=!1,a>=100&&(e.percentage=100,r=!0)}}r&&clearInterval(e.exportLogInfoInterval)})},downloadExportData:function(){var e="/api/dwsurvey/app/v6/answer/export-log/download-answer-xls.do?surveyId="+this.$route.params.dwSurveyId+"&exportLogId="+this.exportLogId;console.log("downUrl",e),window.location.href=e},cancelExportData:function(){null!==this.exportLogId&&null!==this.exportLogInfoInterval&&(clearInterval(this.exportLogInfoInterval),this.percentage=0,this.exportLogId=null,this.isProgress=!1),this.dialogFormVisible=!1}}}},Zq2h:function(e,t,s){},"crP/":function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-row",[s("el-col",{attrs:{span:24}},[s("div",{staticClass:"dw-dcs-main"},[s("div",{staticClass:"dw-dcs-main-survey-title"},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",[s("div",{staticClass:"dw-dcs-main-survey-title-content"},[e._v("\n                "+e._s(e.survey.surveyName)+"\n              ")])]),e._v(" "),s("el-col",{attrs:{span:4}},[s("el-select",{attrs:{value:e.survey.surveyState,placeholder:"请选择"},on:{change:e.surveyStateChange},model:{value:e.survey.surveyState,callback:function(t){e.$set(e.survey,"surveyState",t)},expression:"survey.surveyState"}},[s("el-option",{key:"0",attrs:{value:0,label:"设计中"}}),e._v(" "),s("el-option",{key:"1",attrs:{value:1,label:"发布收集"}}),e._v(" "),s("el-option",{key:"2",attrs:{value:2,label:"收集结束"}})],1)],1)],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step"},[s("div",{staticClass:"dw-dcs-main-survey-step-item",staticStyle:{padding:"13px 16px"}},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isSurveySet},attrs:{to:e.prevPath+"/survey/c/attr/"+e.survey.id}},[s("i",{staticClass:"el-icon-edit"}),e._v(e._s(e.survey.surveyTypeSimpleName)+"设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isAnswerUrl||e.isSiteShare||e.isSiteComp||e.isAnswerWx||e.isAnswerUrlV6},attrs:{to:e.prevPath+"/survey/c/url/"+e.survey.id}},[s("i",{staticClass:"el-icon-link"}),e._v(e._s(e.survey.surveyTypeSimpleName)+"收集")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isSurveyChart||e.isAnswerData},attrs:{to:e.prevPath+"/survey/d/data/"+e.survey.id}},[s("i",{staticClass:"el-icon-s-data"}),e._v(" "+e._s(e.survey.surveyTypeSimpleName)+"数据")])],1),e._v(" "),s("el-col",{staticStyle:{"text-align":"right"},attrs:{span:15}},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handlePush("/v6/diaowen/dw-design/survey/"+e.survey.id)}}},[e._v(e._s(e.survey.surveyTypeSimpleName)+"设计")]),e._v(" "),s("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handlePush(e.prevPath+"/survey/c/url/"+e.survey.id)}}},[e._v("答卷地址")])],1)],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-item",staticStyle:{"padding-left":"16px"}},[s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isSurveySet,expression:"isSurveySet"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSiteComp},attrs:{to:"/v6/diaowen/dw-design/survey/"+e.survey.id}},[s("i",{staticClass:"el-icon-edit"}),e._v(e._s(e.survey.surveyTypeSimpleName)+"设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerWx},attrs:{to:"/v6/diaowen/dw-preview-style/survey/"+e.survey.id}},[s("i",{staticClass:"el-icon-brush"}),e._v("样式设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSurveySet},attrs:{to:e.prevPath+"/survey/c/attr/"+e.survey.id}},[s("i",{staticClass:"el-icon-setting"}),e._v("答卷设置")])],1)],1),e._v(" "),s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isAnswerUrl||e.isSiteShare||e.isSiteComp||e.isAnswerWx||e.isAnswerUrlV6,expression:"isAnswerUrl || isSiteShare || isSiteComp || isAnswerWx || isAnswerUrlV6"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerUrlV6},attrs:{to:e.prevPath+"/survey/c/url/"+e.survey.id}},[s("i",{staticClass:"el-icon-link"}),e._v("答卷地址")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSiteComp},attrs:{to:e.prevPath+"/survey/c/comp/"+e.survey.id}},[s("i",{staticClass:"el-icon-discount"}),e._v("网站组件")])],1)],1),e._v(" "),s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isSurveyChart||e.isAnswerData,expression:"isSurveyChart || isAnswerData"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerData},attrs:{to:e.prevPath+"/survey/d/data/"+e.survey.id}},[s("i",{staticClass:"el-icon-receiving"}),e._v("原始数据")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSurveyChart},attrs:{to:e.prevPath+"/survey/d/chart/"+e.survey.id}},[s("i",{staticClass:"el-icon-discount"}),e._v("默认统计")])],1),e._v(" "),s("el-col",{attrs:{span:3}}),e._v(" "),s("el-col",{attrs:{span:3}})],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-item dw-dcs-main-survey-step-item-status"},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",{attrs:{span:4}},[s("div",[e._v("状态：\n                  "),0===e.survey.surveyState?s("el-tag",{attrs:{size:"mini"}},[e._v("设计中")]):1===e.survey.surveyState?s("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("收集中")]):2===e.survey.surveyState?s("el-tag",{attrs:{type:"info",size:"mini"}},[e._v("收集结束")]):s("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{"disable-transitions":"",size:"mini"}},[e._v("未知")])],1)]),e._v(" "),s("el-col",{attrs:{span:4}},[s("div",[e._v("收集数："+e._s(null!=e.survey.answerNum?e.survey.answerNum:0)+" 份")])]),e._v(" "),s("el-col",{staticStyle:{"text-align":"right"},attrs:{span:16}},[e._v("\n                创建时间："+e._s(e.survey.createDate)+"\n              ")])],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-main"},[e._t("dw-dcs-main-slot",null,{survey:e.survey})],2)])])])],1)],1)},n=[]},eNti:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(s("12Lk")),n=a(s("82+z"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwSurveyAnswerReview",components:{DwAnswerSurvey:n.default,DwAnswerSurveyMain:r.default},props:{dwEsSurveyAnswer:{type:Object,default:function(){}},padPhoneAnBodySpan:{type:Object,default:function(){}}},data:function(){return{thDwEsSurveyAnswer:null,answerProps:{surveyId:null,sid:null,answerId:null,anPwd:""}}},mounted:function(){this.$route.params.hasOwnProperty("sid")&&this.$route.params.hasOwnProperty("answerId")?(this.answerProps.sid=this.$route.params.sid,this.answerProps.answerId=this.$route.params.answerId):null!==this.dwEsSurveyAnswer&&(this.answerProps.surveyId=this.dwEsSurveyAnswer.answerCommon.surveyId,this.answerProps.sid=this.dwEsSurveyAnswer.answerCommon.sid,this.answerProps.answerId=this.dwEsSurveyAnswer.answerCommon.answerId,this.answerProps.anPwd=this.dwEsSurveyAnswer.anPwd)},methods:{}}},fTvU:function(e,t,s){"use strict";s.r(t);var r=s("k0N5"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("j83x"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("Zq2h")},"data-v-0631fdbd",null);t.default=l.exports},fcY9:function(e,t,s){"use strict";s.r(t);var r=s("iq4k"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("crP/"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("5vCQ")},"data-v-d02a181e",null);t.default=l.exports},gA2T:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(s("Xkvr")),n=i(s("fcY9")),a=s("heCB");function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwSurveyAnswerDataListV6",components:{DwSurveyDcsWrapperV6:n.default,DwSurveyAnswerDataList:r.default},data:function(){return{thSurvey:null}},mounted:function(){this.getSurveyInfoV6()},methods:{getSurveyInfoV6:function(){var e=this,t={surveyId:this.$route.params.dwSurveyId};(0,a.getDesignSurveyJsonBySurveyId)(t,function(t){console.debug("design survey",t),e.thSurvey=t})}}}},iq4k:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=s("GUUC"),n=s("GUUC"),a=s("heCB");t.default={name:"DwSurveyDcsWrapperV6",props:{id:{type:String,default:""},isAnswerUrl:{type:Boolean,default:!1},isSurveySet:{type:Boolean,default:!1},isSiteShare:{type:Boolean,default:!1},isSiteComp:{type:Boolean,default:!1},isAnswerWx:{type:Boolean,default:!1},isSurveyChart:{type:Boolean,default:!1},isAnswerData:{type:Boolean,default:!1},isSurveyLog:{type:Boolean,default:!1},isAnswerLog:{type:Boolean,default:!1},isAnswerUrlV6:{type:Boolean,default:!1}},data:function(){return{survey:{sid:"",answerUrl:"",answerUrl1:"",answerUrlQR:"",siteCompCodeRoot:"",surveyState:""},prevPath:"/v6/dw"}},mounted:function(){console.debug(Object({NODE_ENV:"production",DW_API_URL:"",DW_WEB_URL:"",DW_RESOURCE_URL:""})),this.getSurveyInfo(),this.$route.path.indexOf("/v6/lr")>=0&&(this.prevPath="/v6/lr/dw")},methods:{buttonClickA:function(e){window.location.href=e},handlePush:function(e){this.$router.push(e)},surveyStateChange:function(){var e=this;console.debug(this.survey.surveyState),(0,n.dwSurveyUpState)(this.$route.params.dwSurveyId,this.survey.surveyState).then(function(t){200===t.data.resultCode?e.$message.success(e.survey.surveyTypeSimpleName+"状态设置成功"):e.$message.error(e.survey.surveyTypeSimpleName+"状态设置失败")})},getSurveyInfo:function(){var e=this;(0,r.dwSurveyInfo)(this.$route.params.dwSurveyId).then(function(t){var s=t.data.data;e.survey=s,e.survey.answerUrl=location.origin+"/#/diaowen/"+e.survey.sid,e.survey.answerUrl1=location.origin+"/static/diaowen/answer-p.html?sid="+e.survey.sid,e.survey.answerUrlQR="/api/dwsurvey/anon/response/answerTD.do?version=v6&sid="+e.survey.sid,e.survey.answerUrlV6=location.origin+"/#/v6/diaowen/an/"+e.survey.sid,e.survey.siteCompCodeRoot='<div id="dwsurveyWebAnswerCompCode"><div id="dwsurveyWebSiteFixed" style="position: fixed; right: 0px; left: auto; top: 520px; z-index: 99999;"><a target=\'_blank\' id="dwsurveyWebSiteFixedA" href="'+e.survey.answerUrlV6+'" style="background-color: rgb(24, 144, 255); width: 15px; display: block; padding: 10px 6px 10px 10px; color: white; cursor: pointer; float: right; vertical-align: middle; text-decoration: none; font-size: 12px; box-sizing: content-box; line-height: 20px;">问卷调查</a></div></div>',e.survey.surveyDetail.effective=1===s.surveyDetail.effective,e.survey.surveyDetail.effectiveIp=1===s.surveyDetail.effectiveIp,e.survey.surveyDetail.refresh=1===s.surveyDetail.refresh,e.survey.surveyDetail.rule=1===s.surveyDetail.rule,e.survey.surveyDetail.ynEndNum=1===s.surveyDetail.ynEndNum,e.survey.surveyDetail.ynEndTime=1===s.surveyDetail.ynEndTime,e.survey.surveyTypeSimpleName="问卷",(0,a.getSurveyTypeSimpleName)(e.survey)})}}}},j83x:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("dw-survey-dcs-wrapper",{attrs:{"is-site-share":"true"}},[t("div",{attrs:{slot:"dw-dcs-main-slot"},slot:"dw-dcs-main-slot"},[t("div",[t("div",{staticClass:"dw-dcs-main-title"},[t("h4",[this._v("分享到社交网络")]),this._v(" "),t("div",{staticClass:"dw-dcs-main-p"},[this._v("赶快分享您的问卷到各大社交网站，让更多关注您的朋友来回答问卷。")])]),this._v(" "),t("div",{staticClass:"dw-dcs-main-content"})])])])],1)},n=[]},k0N5:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(s("s7of")),n=a(s("sxGJ"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DwSiteShare",components:{DwSurveyDcsWrapper:r.default,Clipboard:n.default},data:function(){return{code:"http://ent.surveyform.cn/#/diaowen/wdhl2uv6e9x",src:"https://ent.surveyform.cn/api/dwsurvey/anon/response/answerTD.do?surveyId=465d3020-0880-4c37-ab09-cc40869758de"}},methods:{copyActiveCode:function(e,t){var s=this,r=new n.default(e.target,{text:function(){return t}});r.on("success",function(e){s.$message({type:"success",message:"复制成功"}),r.off("error"),r.off("success"),r.destroy()}),r.on("error",function(e){s.$message({type:"waning",message:"该浏览器不支持自动复制"}),r.off("error"),r.off("success"),r.destroy()}),r.onClick(e)}}}},kDNu:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-dialog",{staticClass:"dialogRoot",attrs:{visible:e.dialogFormVisible,"append-to-body":"",title:"答卷数据",width:"60%"},on:{"update:visible":function(t){e.dialogFormVisible=t},close:e.closeDialog}},[s("div",{staticStyle:{padding:"0 20px"},attrs:{slot:"default"},slot:"default"},[null!==e.row?s("div",{staticStyle:{height:"600px",overflow:"scroll"}},[s("dw-survey-answer-review",{attrs:{"dw-es-survey-answer":e.row,"pad-phone-an-body-span":e.padPhoneAnBodySpan}})],1):e._e()]),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("关 闭")])],1)])],1)},n=[]},kiRw:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return null!==e.survey?s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("div",{staticStyle:{padding:"5px 10px"}},[s("el-row",{attrs:{type:"flex"}},[s("el-col",{attrs:{span:18}},[s("div",{staticStyle:{"font-size":"14px"}},[s("strong",[e._v("原始数据列表")])])]),e._v(" "),s("el-col",{staticStyle:{"text-align":"right","padding-right":"16px"},attrs:{span:6}},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleExport}},[e._v("导出数据")])],1)],1)],1),e._v(" "),s("div",{staticStyle:{padding:"5px 10px"}},[s("el-form",{staticClass:"dw-form-inline",attrs:{inline:!0,model:e.formInline,"label-width":"50px",size:"medium"}},[s("el-form-item",{attrs:{label:"时间"}},[s("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.formInline.anTime,callback:function(t){e.$set(e.formInline,"anTime",t)},expression:"formInline.anTime"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"IP"}},[s("el-input",{attrs:{placeholder:"请输入查询的问卷标题",clearable:""},model:{value:e.formInline.ip,callback:function(t){e.$set(e.formInline,"ip",t)},expression:"formInline.ip"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"城市"}},[s("el-input",{attrs:{placeholder:"请输入查询的问卷标题",clearable:""},model:{value:e.formInline.city,callback:function(t){e.$set(e.formInline,"city",t)},expression:"formInline.city"}})],1),e._v(" "),s("el-form-item",[s("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.resetSearch}},[e._v("重置")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryList(1)}}},[e._v("查询")])],1)],1)],1)]),e._v(" "),s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:""}},[s("el-table-column",{attrs:{type:"selection",width:"45"}}),e._v(" "),s("el-table-column",{attrs:{label:"序号",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(e.pageSize*(e.currentPage-1)+t.$index+1))])]}}],null,!1,385855685)}),e._v(" "),s("el-table-column",{attrs:{label:"答卷IP",prop:"answerCommon.anIp.ip"}}),e._v(" "),s("el-table-column",{attrs:{label:"答卷地区",prop:"answerCommon.anIp.city"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(null!=t.row.answerCommon.anIp.city?t.row.answerCommon.anIp.city:"-"))])]}}],null,!1,1323600062)}),e._v(" "),s("el-table-column",{attrs:{label:"回答时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(t.row.answerCommon.anTime.endAnDate))])]}}],null,!1,845393646)}),e._v(" "),s("el-table-column",{attrs:{label:"回答用时"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(null!=t.row.answerCommon.anTime.totalTime?e.secondsToHms(Math.floor(t.row.answerCommon.anTime.totalTime/1e3)):0))])]}}],null,!1,2246649381)}),e._v(" "),s("el-table-column",{attrs:{label:"回答题数"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(null!=t.row.answerCommon.anState.anQuNum?t.row.answerCommon.anState.anQuNum:0)+" 题")])]}}],null,!1,1097718576)}),e._v(" "),null!==e.survey&&(e.survey.hasOwnProperty("surveyType")&&"exam"===e.survey.surveyType||e.survey.hasOwnProperty("surveyAttrs")&&e.survey.surveyAttrs.scoreAttr.enabled)?s("el-table-column",{attrs:{label:"分数"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(null!=t.row.answerCommon.sumScore?t.row.answerCommon.sumScore:0)+" 分")])]}}],null,!1,1094529710)}):e._e(),e._v(" "),s("el-table-column",{attrs:{label:"操作",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button-group",[s("el-tooltip",{attrs:{effect:"dark",content:"查看数据",placement:"top"}},[s("el-button",{attrs:{size:"mini",icon:"el-icon-view"},on:{click:function(s){return e.answerView(t.row)}}})],1),e._v(" "),s("el-tooltip",{attrs:{effect:"dark",content:"删除数据",placement:"top"}},[s("el-button",{attrs:{size:"mini",icon:"el-icon-delete"},on:{click:function(s){return e.handleDelete(t.$index,t.row)}}})],1)],1)]}}],null,!1,2801452500)})],1),e._v(" "),s("div",{staticClass:"dw-pagination"},[s("el-pagination",{attrs:{"page-size":e.pageSize,"current-page":e.currentPage,total:e.total,background:"",layout:"total, prev, pager, next"},on:{"current-change":e.handleCurrentChange}}),e._v(" "),e.total>1e4?s("span",{staticStyle:{"font-size":"10px",color:"grey"}},[e._v(" 仅显示前1万条数据，更多可以通过改变查询条件或导出 ")]):e._e()],1),e._v(" "),s("el-dialog",{attrs:{visible:e.dialogFormVisible,"append-to-body":"",title:"导出答卷数据",width:"40%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[s("div",{staticStyle:{"line-height":"30px"}},[e._v("是否同时下载上传题的文件")]),e._v(" "),s("div",{staticStyle:{color:"grey","line-height":"30px","font-size":"12px"}},[s("span",[e._v("如果有上传题，选择压缩下载可能比较占用系统资源及时间，请在空闲时间压缩下载")])]),e._v(" "),s("div",{staticStyle:{padding:"10px"}},[s("el-switch",{attrs:{"active-text":"同时压缩上传的文件并下载","inactive-text":"仅下载数据Excel","active-value":"1","inactive-value":"0"},model:{value:e.expUpQu,callback:function(t){e.expUpQu=t},expression:"expUpQu"}})],1),e._v(" "),s("div",{staticStyle:{padding:"10px"}},[e._v("\n        数据类型\n        "),s("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:e.handleState,callback:function(t){e.handleState=t},expression:"handleState"}},[s("el-option",{attrs:{value:0,label:"未审核"}}),e._v(" "),s("el-option",{attrs:{value:1,label:"审核的数据"}}),e._v(" "),s("el-option",{attrs:{value:300,label:"甄别的数据"}}),e._v(" "),s("el-option",{attrs:{value:100,label:"全部有效数据（包含未审核与审核）"}})],1)],1),e._v(" "),s("div",{staticStyle:{padding:"10px"}},[e._v("\n        单个线程最多导出\n        "),s("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:e.threadMax,callback:function(t){e.threadMax=t},expression:"threadMax"}},[s("el-option",{attrs:{value:500,label:"500条"}}),e._v(" "),s("el-option",{attrs:{value:1e3,label:"1000条"}}),e._v(" "),s("el-option",{attrs:{value:2e3,label:"2000条"}}),e._v(" "),s("el-option",{attrs:{value:4e3,label:"4000条"}})],1)],1),e._v(" "),s("div",{staticStyle:{padding:"10px"}},[e._v("\n        导出的数据\n        "),s("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:e.expDataContent,callback:function(t){e.expDataContent=t},expression:"expDataContent"}},[s("el-option",{attrs:{value:1,label:"原始答卷数据"}}),e._v(" "),s("el-option",{attrs:{value:2,label:"答卷选项分值"}})],1)],1),e._v(" "),s("div",{staticStyle:{padding:"10px"}},[s("el-progress",{attrs:{percentage:e.percentage,color:e.customColor,"text-inside":!0,"stroke-width":26}})],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:e.cancelExportData}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{disabled:e.isProgress,type:"primary"},on:{click:e.executeExportData}},[e._v("开始导出")]),e._v(" "),s("el-button",{attrs:{disabled:e.percentage<100,type:"primary"},on:{click:e.downloadExportData}},[e._v("下载数据")])],1)]),e._v(" "),s("dw-survey-answer-info-dialog",{ref:"dwAnswerInfoDialog",on:{"refresh-data":function(t){return e.queryList(null)}}})],1):e._e()},n=[]},mfmh:function(e,t,s){"use strict";s.r(t);var r=s("9q9F"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("kDNu"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("EEXx")},"data-v-04b91374",null);t.default=l.exports},mftc:function(e,t,s){},qfdw:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-row",[s("el-col",{attrs:{span:20,offset:2}},[s("div",{staticClass:"dw-dcs-main"},[s("div",{staticClass:"dw-dcs-main-survey-title"},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",[s("div",{staticClass:"dw-dcs-main-survey-title-content"},[null!=e.survey.surveyNameText?s("div",{domProps:{textContent:e._s(e.survey.surveyNameText)}}):s("div",{domProps:{innerHTML:e._s(e.survey.surveyName)}})])]),e._v(" "),s("el-col",{attrs:{span:4}},[s("el-select",{attrs:{value:e.survey.surveyState,placeholder:"请选择"},on:{change:e.surveyStateChange},model:{value:e.survey.surveyState,callback:function(t){e.$set(e.survey,"surveyState",t)},expression:"survey.surveyState"}},[s("el-option",{key:"0",attrs:{value:0,label:"设计中"}}),e._v(" "),s("el-option",{key:"1",attrs:{value:1,label:"发布收集"}}),e._v(" "),s("el-option",{key:"2",attrs:{value:2,label:"收集结束"}})],1)],1)],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step"},[s("div",{staticClass:"dw-dcs-main-survey-step-item",staticStyle:{padding:"13px 16px"}},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isSurveySet},attrs:{to:e.prevPath+"/survey/c/attr/"+e.survey.id}},[s("i",{staticClass:"el-icon-edit"}),e._v("问卷设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isAnswerUrl||e.isSiteShare||e.isSiteComp||e.isAnswerWx||e.isAnswerUrlV6},attrs:{to:e.prevPath+"/survey/c/url/"+e.survey.id}},[s("i",{staticClass:"el-icon-link"}),e._v("问卷收集")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link dw-link-1",class:{"dw-link-primary":e.isSurveyChart||e.isAnswerData},attrs:{to:e.prevPath+"/survey/d/chart/"+e.survey.id}},[s("i",{staticClass:"el-icon-s-data"}),e._v("问卷数据")])],1),e._v(" "),s("el-col",{staticStyle:{"text-align":"right"},attrs:{span:15}},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handlePush("/v6/diaowen/dw-design/survey/"+e.survey.id)}}},[e._v("问卷设计")]),e._v(" "),s("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handlePush(e.prevPath+"/survey/c/url/"+e.survey.id)}}},[e._v("答卷地址")])],1)],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-item",staticStyle:{"padding-left":"16px"}},[s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isSurveySet,expression:"isSurveySet"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSiteComp},attrs:{to:e.prevPath+"/survey/c/comp/"+e.survey.id}},[s("i",{staticClass:"el-icon-edit"}),e._v("问卷设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerWx},attrs:{to:e.prevPath+"/survey/c/weixin/"+e.survey.id}},[s("i",{staticClass:"el-icon-brush"}),e._v("样式设计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSurveySet},attrs:{to:e.prevPath+"/survey/c/attr/"+e.survey.id}},[s("i",{staticClass:"el-icon-setting"}),e._v("答卷设置")])],1)],1),e._v(" "),s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isAnswerUrl||e.isSiteShare||e.isSiteComp||e.isAnswerWx||e.isAnswerUrlV6,expression:"isAnswerUrl || isSiteShare || isSiteComp || isAnswerWx || isAnswerUrlV6"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerUrl},attrs:{to:e.prevPath+"/survey/c/url/"+e.survey.id}},[s("i",{staticClass:"el-icon-link"}),e._v("答卷地址")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerUrlV6},attrs:{to:e.prevPath+"/survey/c/url/"+e.survey.id}},[s("i",{staticClass:"el-icon-link"}),e._v("答卷地址V6")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSurveySet},attrs:{to:e.prevPath+"/survey/c/attr/"+e.survey.id}},[s("i",{staticClass:"el-icon-setting"}),e._v("答卷设置")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSiteComp},attrs:{to:e.prevPath+"/survey/c/comp/"+e.survey.id}},[s("i",{staticClass:"el-icon-discount"}),e._v("网站组件")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerWx},attrs:{to:e.prevPath+"/survey/c/weixin/"+e.survey.id}},[s("i",{staticClass:"el-icon-chat-dot-round"}),e._v("微信收集")])],1)],1),e._v(" "),s("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isSurveyChart||e.isAnswerData,expression:"isSurveyChart || isAnswerData"}]},[s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isSurveyChart},attrs:{to:e.prevPath+"/survey/d/chart/"+e.survey.id}},[s("i",{staticClass:"el-icon-discount"}),e._v("默认统计")])],1),e._v(" "),s("el-col",{attrs:{span:3}},[s("router-link",{staticClass:"dw-link",class:{"dw-link-primary":e.isAnswerData},attrs:{to:e.prevPath+"/survey/d/data/"+e.survey.id}},[s("i",{staticClass:"el-icon-receiving"}),e._v("原始数据")])],1),e._v(" "),s("el-col",{attrs:{span:3}}),e._v(" "),s("el-col",{attrs:{span:3}})],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-item dw-dcs-main-survey-step-item-status"},[s("el-row",{attrs:{type:"flex",justify:"space-between",align:"middle"}},[s("el-col",{attrs:{span:4}},[s("div",[e._v("状态：\n                  "),0===e.survey.surveyState?s("el-tag",{attrs:{size:"mini"}},[e._v("设计中")]):1===e.survey.surveyState?s("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("收集中")]):2===e.survey.surveyState?s("el-tag",{attrs:{type:"info",size:"mini"}},[e._v("收集结束")]):s("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{"disable-transitions":"",size:"mini"}},[e._v("未知")])],1)]),e._v(" "),s("el-col",{attrs:{span:4}},[s("div",[e._v("收集数："+e._s(null!=e.survey.answerNum?e.survey.answerNum:0)+" 份")])]),e._v(" "),s("el-col",{staticStyle:{"text-align":"right"},attrs:{span:16}},[e._v("\n                创建时间："+e._s(e.survey.createDate)+"\n              ")])],1)],1),e._v(" "),s("div",{staticClass:"dw-dcs-main-survey-step-main"},[e._t("dw-dcs-main-slot",null,{survey:e.survey})],2)])])])],1)],1)},n=[]},s7of:function(e,t,s){"use strict";s.r(t);var r=s("C/hQ"),n=s.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){s.d(t,e,function(){return r[e]})}(a);var i=s("qfdw"),o=s("JFUb"),l=Object(o.a)(n.a,i.a,i.b,!1,function(e){s("TGtZ")},"data-v-495025b0",null);t.default=l.exports},sxGJ:function(e,t,s){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
var r;r=function(){return function(){var e={686:function(e,t,s){"use strict";s.d(t,{default:function(){return g}});var r=s(279),n=s.n(r),a=s(370),i=s.n(a),o=s(817),l=s.n(o);function u(e){try{return document.execCommand(e)}catch(e){return!1}}var c=function(e){var t=l()(e);return u("cut"),t},d=function(e,t){var s=function(e){var t="rtl"===document.documentElement.getAttribute("dir"),s=document.createElement("textarea");s.style.fontSize="12pt",s.style.border="0",s.style.padding="0",s.style.margin="0",s.style.position="absolute",s.style[t?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return s.style.top="".concat(r,"px"),s.setAttribute("readonly",""),s.value=e,s}(e);t.container.appendChild(s);var r=l()(s);return u("copy"),s.remove(),r},v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},s="";return"string"==typeof e?s=d(e,t):e instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===e||void 0===e?void 0:e.type)?s=d(e.value,t):(s=l()(e),u("copy")),s};function p(e){"@babel/helpers - typeof";return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e){"@babel/helpers - typeof";return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function m(e,t){return(m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function w(e){return(w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){var s="data-clipboard-".concat(e);if(t.hasAttribute(s))return t.getAttribute(s)}var g=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&m(e,t)}(o,n());var t,s,r,a=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}();return function(){var s,r=w(e);if(t){var n=w(this).constructor;s=Reflect.construct(r,arguments,n)}else s=r.apply(this,arguments);return function(e,t){return!t||"object"!==f(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}(this,s)}}(o);function o(e,t){var s;return function(e,t){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}(this),(s=a.call(this)).resolveOptions(t),s.listenClick(e),s}return t=o,r=[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return v(e,t)}},{key:"cut",value:function(e){return c(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,s=!!document.queryCommandSupported;return t.forEach(function(e){s=s&&!!document.queryCommandSupported(e)}),s}}],(s=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===f(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=i()(e,"click",function(e){return t.onClick(e)})}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,s=this.action(t)||"copy",r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,s=void 0===t?"copy":t,r=e.container,n=e.target,a=e.text;if("copy"!==s&&"cut"!==s)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==n){if(!n||"object"!==p(n)||1!==n.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===s&&n.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===s&&(n.hasAttribute("readonly")||n.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return a?v(a,{container:r}):n?"cut"===s?c(n):v(n,{container:r}):void 0}({action:s,container:this.container,target:this.target(t),text:this.text(t)});this.emit(r?"success":"error",{action:s,text:r,trigger:t,clearSelection:function(){t&&t.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return h("action",e)}},{key:"defaultTarget",value:function(e){var t=h("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return h("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}])&&y(t.prototype,s),r&&y(t,r),o}()},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}e.exports=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}},438:function(e,t,s){var r=s(828);function n(e,t,s,n,a){var i=function(e,t,s,n){return function(s){s.delegateTarget=r(s.target,t),s.delegateTarget&&n.call(e,s)}}.apply(this,arguments);return e.addEventListener(s,i,a),{destroy:function(){e.removeEventListener(s,i,a)}}}e.exports=function(e,t,s,r,a){return"function"==typeof e.addEventListener?n.apply(null,arguments):"function"==typeof s?n.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,function(e){return n(e,t,s,r,a)}))}},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var s=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===s||"[object HTMLCollection]"===s)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,t,s){var r=s(879),n=s(438);e.exports=function(e,t,s){if(!e&&!t&&!s)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(s))throw new TypeError("Third argument must be a Function");if(r.node(e))return function(e,t,s){return e.addEventListener(t,s),{destroy:function(){e.removeEventListener(t,s)}}}(e,t,s);if(r.nodeList(e))return function(e,t,s){return Array.prototype.forEach.call(e,function(e){e.addEventListener(t,s)}),{destroy:function(){Array.prototype.forEach.call(e,function(e){e.removeEventListener(t,s)})}}}(e,t,s);if(r.string(e))return function(e,t,s){return n(document.body,e,t,s)}(e,t,s);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var s=e.hasAttribute("readonly");s||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),s||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),n=document.createRange();n.selectNodeContents(e),r.removeAllRanges(),r.addRange(n),t=r.toString()}return t}},279:function(e){function t(){}t.prototype={on:function(e,t,s){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:s}),this},once:function(e,t,s){var r=this;function n(){r.off(e,n),t.apply(s,arguments)}return n._=t,this.on(e,n,s)},emit:function(e){for(var t=[].slice.call(arguments,1),s=((this.e||(this.e={}))[e]||[]).slice(),r=0,n=s.length;r<n;r++)s[r].fn.apply(s[r].ctx,t);return this},off:function(e,t){var s=this.e||(this.e={}),r=s[e],n=[];if(r&&t)for(var a=0,i=r.length;a<i;a++)r[a].fn!==t&&r[a].fn._!==t&&n.push(r[a]);return n.length?s[e]=n:delete s[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function s(r){if(t[r])return t[r].exports;var n=t[r]={exports:{}};return e[r](n,n.exports,s),n.exports}return s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,{a:t}),t},s.d=function(e,t){for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s(686)}().default},e.exports=r()},tqcQ:function(e,t,s){},up8O:function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("dw-survey-dcs-wrapper-v6",{attrs:{"is-answer-url-v6":!0},scopedSlots:e._u([{key:"dw-dcs-main-slot",fn:function(t){var r=t.survey;return[s("div",[s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("答卷地址")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("复制下面的问卷链接到QQ，Email等工具中直接发给被用户")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"},[s("div",{staticClass:"dw-dcs-main-title"},[s("div",{staticClass:"dw-dcs-main-p"},[e._v("V6新版答卷地址")])]),e._v(" "),s("el-input",{staticStyle:{width:"500px"},attrs:{id:"copyCodeInput",readonly:""},model:{value:r.answerUrlV6,callback:function(t){e.$set(r,"answerUrlV6",t)},expression:"survey.answerUrlV6"}}),e._v(" "),s("el-button-group",[s("el-button",{attrs:{type:"primary",icon:"el-icon-copy-document"},on:{click:function(t){return e.copyActiveCode(t,r.answerUrlV6)}}},[e._v("复制地址")]),e._v(" "),s("el-link",{attrs:{href:r.answerUrlV6,underline:!1,target:"_blank"}},[s("el-button",{staticStyle:{"border-bottom-left-radius":"0px","border-top-left-radius":"0px"},attrs:{icon:"el-icon-link"}},[e._v("打开"+e._s(r.surveyTypeSimpleName))])],1)],1)],1)]),e._v(" "),s("div",[s("div",{staticClass:"dw-dcs-main-title"},[s("h4",[e._v("二维码地址")]),e._v(" "),s("div",{staticClass:"dw-dcs-main-p"},[e._v("通过手机扫一扫，或下载二维码，即可进行问卷数据收集。")])]),e._v(" "),s("div",{staticClass:"dw-dcs-main-content"},[s("el-row",{attrs:{type:"flex",justify:"start",align:"middle"}},[s("el-col",{attrs:{span:4}},[s("el-image",{staticClass:"dw-dcs-main-img",attrs:{src:r.answerUrlQR}},[s("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e._v("\n                    加载中"),s("span",{staticClass:"dot"},[e._v("...")])])])],1),e._v(" "),s("el-col",{attrs:{span:20}},[s("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:function(t){return e.downloadAnswerImg(r.answerUrlQR+"&down=1")}}},[e._v("下载二维码")])],1)],1)],1)])])]}}])})],1)},n=[]},"vk/N":function(e,t,s){"use strict";s.d(t,"a",function(){return r}),s.d(t,"b",function(){return n});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"answerReviewRoot"},[s("el-row",[s("el-col",[s("div",{staticStyle:{"background-color":"#dfdfe0"}},[null!==e.dwEsSurveyAnswer&&void 0!==e.dwEsSurveyAnswer?s("div",{staticStyle:{padding:"10px"}},[s("el-descriptions",{attrs:{column:2,title:"答卷基本信息",border:""}},[s("template",{slot:"extra"}),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-key"}),e._v("  ID")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.sid)+"/"+e._s(e.dwEsSurveyAnswer.esId)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-user"}),e._v("  用户")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.anUser.userName)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-date"}),e._v("  时间")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.anTime.endAnDate)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-place"}),e._v("  IP\n              ")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.anIp.ip)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-office-building"}),e._v("  IP地址\n              ")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.anIp.addr)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-star-off"}),e._v("  总分\n              ")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.answerCommon.sumScore)+"\n            ")],2),e._v(" "),s("el-descriptions-item",[s("template",{slot:"label"},[s("i",{staticClass:"el-icon-user"}),e._v("  密码")]),e._v("\n              "+e._s(e.dwEsSurveyAnswer.anPwd)+"\n            ")],2)],2)],1):e._e(),e._v(" "),s("div",{staticStyle:{"margin-top":"0"}},[s("dw-answer-survey",{attrs:{"answer-props":e.answerProps,"ext-props":{anBodySpan:e.padPhoneAnBodySpan,anBodyStyle:{minHeight:"630px",height:"auto"},isPreview:!0,readonly:!0,isShowScore:!0}}})],1)])])],1)],1)},n=[]}}]);