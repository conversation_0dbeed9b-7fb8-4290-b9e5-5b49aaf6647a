package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.base.service.BaseServiceImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.UserGridRoleDao;
import net.diaowen.dwsurvey.entity.UserGridRole;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("userGridRoleManager")
@Transactional
public class UserGridRoleManagerImpl extends BaseServiceImpl<UserGridRole, String> implements UserGridRoleManager {

    @Autowired
    private UserGridRoleDao userGridRoleDao;

    @Autowired
    public void setBaseDao(UserGridRoleDao userGridRoleDao) {
        super.setBaseDao(userGridRoleDao);
    }

    @Override
    public Page<UserGridRole> findByCondition(Page<UserGridRole> page, String userId, String gridId, String roleCode, Integer status) {
        List<Criterion> criterions = new ArrayList<>();
        if (StringUtils.hasText(userId)) {
            criterions.add(Restrictions.eq("userId", userId));
        }
        if (StringUtils.hasText(gridId)) {
            criterions.add(Restrictions.eq("gridId", gridId));
        }
        if (StringUtils.hasText(roleCode)) {
            criterions.add(Restrictions.eq("roleCode", roleCode));
        }
        if (status != null) {
            criterions.add(Restrictions.eq("status", status));
        }
        return userGridRoleDao.findByCondition(page, criterions);
    }

    @Override
    public List<UserGridRole> findByUserId(String userId) {
        return userGridRoleDao.findBy("userId", userId, null);
    }

    @Override
    public List<UserGridRole> findByGridCode(String gridCode) {
        return userGridRoleDao.findBy("gridCode", gridCode, null);
    }

    @Override
    public UserGridRole findByUserIdAndGridCode(String userId, String gridCode) {
        return userGridRoleDao.findUniqueBy("userId", userId, "gridCode", gridCode);
    }

    @Override
    public List<UserGridRole> findByRoleType(String roleType) {
        return userGridRoleDao.findBy("roleType", roleType, null);
    }

    @Override
    public String getUserPermissions(String userId, String gridCode) {
        UserGridRole role = findByUserIdAndGridCode(userId, gridCode);
        return role != null ? role.getPermissions() : null;
    }

    @Override
    public boolean hasPermission(String userId, String gridCode, String permission) {
        String permissions = getUserPermissions(userId, gridCode);
        return permissions != null && permissions.contains(permission);
    }

    @Override
    public UserGridRole assignRole(String userId, String gridCode, String roleType, String permissions, String assignedBy) {
        UserGridRole role = new UserGridRole();
        role.setUserId(userId);
        role.setGridCode(gridCode);
        role.setRoleType(roleType);
        role.setPermissions(permissions);
        role.setAssignedBy(assignedBy);
        role.setAssignedDate(new Date());
        role.setStatus(1); // Active
        userGridRoleDao.save(role);
        return role;
    }

    @Override
    public UserGridRole updateRole(String roleId, String roleType, String permissions, String updatedBy) {
        UserGridRole role = userGridRoleDao.get(roleId);
        if (role != null) {
            role.setRoleType(roleType);
            role.setPermissions(permissions);
            role.setUpdatedBy(updatedBy);
            role.setUpdateDate(new Date());
            userGridRoleDao.update(role);
        }
        return role;
    }

    @Override
    public boolean removeRole(String roleId, String userId) {
        UserGridRole role = userGridRoleDao.get(roleId);
        if (role != null && role.getUserId().equals(userId)) {
            userGridRoleDao.delete(role);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateRoleStatus(String roleId, Integer status, String updatedBy) {
        UserGridRole role = userGridRoleDao.get(roleId);
        if (role != null) {
            role.setStatus(status);
            role.setUpdatedBy(updatedBy);
            role.setUpdateDate(new Date());
            userGridRoleDao.update(role);
            return true;
        }
        return false;
    }

    @Override
    public List<String> getUserManagedGrids(String userId) {
        return userGridRoleDao.findManagedGridIds(userId);
    }

    @Override
    public List<UserGridRole> getGridCollectors(String gridCode) {
        return userGridRoleDao.findCollectorsByGrid(gridCode);
    }

    @Override
    public List<String> findGridIdsByUserId(String userId) {
        return userGridRoleDao.findGridIdsByUserId(userId);
    }

    @Override
    public boolean hasGridPermission(String userId, String gridCode, String permission) {
        return hasPermission(userId, gridCode, permission);
    }

    @Override
    public List<UserGridRole> findByGridId(String gridId) {
        return userGridRoleDao.findBy("gridId", gridId, null);
    }

    @Override
    public List<String> findUserIdsByGridId(String gridId) {
        List<UserGridRole> roles = findByGridId(gridId);
        return roles.stream().map(UserGridRole::getUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public boolean assignUserToGrid(String userId, String gridId, String roleType, String permissions) {
        UserGridRole role = new UserGridRole();
        role.setUserId(userId);
        role.setGridId(gridId);
        role.setRoleType(roleType);
        role.setPermissions(permissions);
        role.setAssignedDate(new Date());
        role.setStatus(1);
        userGridRoleDao.save(role);
        return true;
    }

    @Override
    public boolean removeUserFromGrid(String userId, String gridId) {
        return userGridRoleDao.removeUserFromGrid(userId, gridId) > 0;
    }

    @Override
    public boolean updateUserRole(String userId, String gridId, String roleType) {
        return userGridRoleDao.updateUserRole(userId, gridId, roleType) > 0;
    }

    @Override
    public boolean updateUserPermissions(String userId, String gridId, String permissions) {
        return userGridRoleDao.updateUserPermissions(userId, gridId, permissions) > 0;
    }

    @Override
    public List<UserGridRole> findManagersByGrid(String gridId) {
        return userGridRoleDao.findManagersByGrid(gridId);
    }

    @Override
    public List<UserGridRole> findCollectorsByGrid(String gridId) {
        return userGridRoleDao.findCollectorsByGrid(gridId);
    }

    @Override
    public boolean isGridManager(String userId, String gridId) {
        return userGridRoleDao.isGridManager(userId, gridId);
    }

    @Override
    public boolean isDataCollector(String userId, String gridId) {
        return userGridRoleDao.isDataCollector(userId, gridId);
    }

    @Override
    public List<String> findManagedGridIds(String userId) {
        return userGridRoleDao.findManagedGridIds(userId);
    }

    @Override
    public void batchAssignUsers(List<String> userIds, String gridId, String roleCode, String permissions) {
        userGridRoleDao.batchAssignUsers(userIds, gridId, roleCode, permissions);
    }

    @Override
    public void batchRemoveUsers(List<String> userIds, String gridId) {
        userGridRoleDao.batchRemoveUsers(userIds, gridId);
    }

    @Override
    public long countByRole(String roleType) {
        return userGridRoleDao.countByRole(roleType);
    }
}