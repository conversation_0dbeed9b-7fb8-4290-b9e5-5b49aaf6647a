package net.diaowen.dwsurvey.service;

import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.entity.UserGridRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 任务调度服务
 * 处理任务的自动分配、提醒和状态更新
 * <AUTHOR> Team
 */
@Service
public class TaskScheduleService {

    @Autowired
    private CityTaskManager cityTaskManager;
    
    @Autowired
    private UserGridRoleManager userGridRoleManager;
    
    @Autowired
    private TaskPushService taskPushService;

    /**
     * 自动分配任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void autoAssignTasks() {
        try {
            // 获取所有未分配的任务
            List<CityTask> unassignedTasks = cityTaskManager.findUnassignedTasks();
            
            for (CityTask task : unassignedTasks) {
                // 根据网格和任务类型自动分配任务
                String assigneeId = findBestAssignee(task);
                if (assigneeId != null) {
                    // 分配任务
                    cityTaskManager.assignTask(task.getId(), task.getGridId(), assigneeId);
                    
                    // 推送新任务通知
                    taskPushService.pushTaskToUser(assigneeId, task, "NEW_TASK_ASSIGNED");
                    
                    System.out.println("自动分配任务: " + task.getTaskName() + " -> " + assigneeId);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 任务截止提醒
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void taskDeadlineReminder() {
        try {
            Date now = new Date();
            Date reminderTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后
            
            // 获取即将到期的任务
            List<CityTask> upcomingTasks = cityTaskManager.findTasksNearDeadline("SYSTEM", 24);
            
            for (CityTask task : upcomingTasks) {
                if (task.getAssigneeId() != null) {
                    // 推送截止提醒
                    taskPushService.pushTaskToUser(task.getAssigneeId(), task, "TASK_DEADLINE_REMINDER");
                    
                    System.out.println("发送截止提醒: " + task.getTaskName() + " -> " + task.getAssigneeId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查逾期任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void checkOverdueTasks() {
        try {
            Date now = new Date();
            
            // 获取已逾期但状态未更新的任务
            List<CityTask> overdueTasks = cityTaskManager.findOverdueTasks("SYSTEM");
            
            for (CityTask task : overdueTasks) {
                // 更新任务状态为逾期
                cityTaskManager.updateTaskStatus(task.getId(), 4, "SYSTEM"); // 4表示逾期状态
                
                // 推送逾期通知
                if (task.getAssigneeId() != null) {
                    taskPushService.pushTaskToUser(task.getAssigneeId(), task, "TASK_OVERDUE");
                }
                
                System.out.println("任务逾期: " + task.getTaskName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 清理WebSocket会话
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void cleanupWebSocketSessions() {
        try {
            taskPushService.cleanupClosedSessions();
            System.out.println("WebSocket会话清理完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成任务统计数据
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateTaskStatistics() {
        try {
            // 生成昨天的任务统计数据
            Date yesterday = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000);
            cityTaskManager.generateDailyStatistics(yesterday);
            
            System.out.println("任务统计数据生成完成: " + yesterday);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
    /**
     * 查找最佳任务分配人
     */
    private String findBestAssignee(CityTask task) {
        try {
            // 获取网格内的数据采集员
            List<UserGridRole> collectors = userGridRoleManager.findCollectorsByGrid(task.getGridCode());
            
            if (collectors.isEmpty()) {
                // 如果没有数据采集员，查找网格管理员
                List<UserGridRole> managers = userGridRoleManager.findManagersByGrid(task.getGridCode());
                if (!managers.isEmpty()) {
                    return managers.get(0).getUserId();
                }
                return null;
            }
            
            // 简单的负载均衡：选择当前任务最少的采集员
            String bestAssignee = null;
            int minTaskCount = Integer.MAX_VALUE;
            
            for (UserGridRole collector : collectors) {
                int taskCount = cityTaskManager.countActiveTasksByAssignee(collector.getUserId());
                if (taskCount < minTaskCount) {
                    minTaskCount = taskCount;
                    bestAssignee = collector.getUserId();
                }
            }
            
            return bestAssignee;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 手动触发任务自动分配
     */
    public void triggerAutoAssign() {
        autoAssignTasks();
    }

    /**
     * 手动触发截止提醒
     */
    public void triggerDeadlineReminder() {
        taskDeadlineReminder();
    }

    /**
     * 手动触发逾期检查
     */
    public void triggerOverdueCheck() {
        checkOverdueTasks();
    }
}