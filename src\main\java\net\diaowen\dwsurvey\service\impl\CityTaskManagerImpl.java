package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.base.service.BaseServiceImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.CityTaskDao;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.service.CityTaskManager;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service("cityTaskManager")
public class CityTaskManagerImpl extends BaseServiceImpl<CityTask, String> implements CityTaskManager {

    @Autowired
    private CityTaskDao cityTaskDao;

    @PostConstruct
    public void init() {
        super.setBaseDao(cityTaskDao);
    }

    @Override
    public boolean assignTask(String taskId, String gridId, String userId, String assignedBy) {
        CityTask task = cityTaskDao.get(taskId);
        if (task != null) {
            task.setGridId(gridId);
            task.setAssignedUserId(userId);
            task.setAssignedBy(assignedBy);
            task.setAssignedDate(new Date());
            task.setStatus(1); // Status: In Progress
            cityTaskDao.update(task);
            return true;
        }
        return false;
    }

    @Override
    public Page<CityTask> findMyTasks(Page<CityTask> page, String gridCode, Integer status) {
        List<Criterion> criterions = new ArrayList<>();
        if (status != null) {
            criterions.add(Restrictions.eq("status", status));
        }
        if (gridCode != null) {
            criterions.add(Restrictions.eq("gridCode", gridCode));
        }
        return cityTaskDao.findByCondition(page, criterions);
    }

    @Override
    public List<CityTask> findTasksByGrid(String gridCode, Integer status) {
        String hql = "from CityTask where gridCode = ?0 and status = ?1";
        return cityTaskDao.find(hql, gridCode, status);
    }

    @Override
    public Map<String, Object> getTaskStatsByUser(String userId) {
        String hql = "select status, count(*) from CityTask where assignedUserId = ?0 group by status";
        List<Object[]> results = cityTaskDao.find(hql, userId);
        Map<String, Object> stats = new HashMap<>();
        for (Object[] result : results) {
            stats.put("status_" + result[0], result[1]);
        }
        return stats;
    }

    @Override
    public List<CityTask> findNearbyTasks(Double longitude, Double latitude, Double radius, String userId) {
        // This is a simplified implementation. A real implementation would use spatial queries.
        return new ArrayList<>();
    }

    @Override
    public List<CityTask> findTasksNearDeadline(String userId, Integer hours) {
        Date now = new Date();
        Date deadline = new Date(now.getTime() + hours * 3600 * 1000);
        String hql = "from CityTask where assignedUserId = ?0 and deadline <= ?1 and status = 1";
        return cityTaskDao.find(hql, userId, deadline);
    }

    @Override
    public Page<CityTask> searchTasks(Page<CityTask> page, String userId, String keyword) {
        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.eq("assignedUserId", userId));
        criterions.add(Restrictions.like("taskTitle", "%" + keyword + "%"));
        return cityTaskDao.findByCondition(page, criterions);
    }

    @Override
    public int batchUpdateStatus(List<String> taskIds, Integer status, String updatedBy) {
        return cityTaskDao.batchUpdateStatus(taskIds, status, updatedBy);
    }

    @Override
    public List<CityTask> autoAssignTasks(String userId, String gridCode) {
        // Simplified logic
        return new ArrayList<>();
    }

    @Override
    public List<CityTask> smartSortTasks(List<CityTask> tasks, String sortType, Double userLongitude, Double userLatitude) {
        // Simplified logic
        return tasks;
    }

    @Override
    public boolean completeTask(String taskId, String formData, String attachments, String userId) {
        CityTask task = cityTaskDao.get(taskId);
        if (task != null && task.getAssignedUserId().equals(userId)) {
            task.setStatus(2); // Completed
            task.setFormData(formData);
            task.setAttachments(attachments);
            task.setCompletedTime(new Date());
            task.setUpdatedBy(userId);
            cityTaskDao.update(task);
            return true;
        }
        return false;
    }

    @Override
    public CityTask getTaskDetail(String taskId, String userId) {
        return cityTaskDao.get(taskId);
    }

    @Override
    public CityTask createTask(CityTask task, String createdBy) {
        task.setCreatedBy(createdBy);
        task.setCreateDate(new Date());
        return cityTaskDao.save(task);
    }

    @Override
    public CityTask updateTask(CityTask task, String updatedBy) {
        task.setUpdatedBy(updatedBy);
        task.setUpdateDate(new Date());
        cityTaskDao.update(task);
        return task;
    }

    @Override
    public boolean deleteTask(String taskId, String userId) {
        CityTask task = cityTaskDao.get(taskId);
        if (task != null) {
            cityTaskDao.delete(task);
            return true;
        }
        return false;
    }

    @Override
    public List<CityTask> findUnassignedTasks() {
        return cityTaskDao.find("from CityTask where assignedUserId is null");
    }

    @Override
    public List<CityTask> findOverdueTasks(String userId) {
        return cityTaskDao.find("from CityTask where assignedUserId = ?0 and deadline < now() and status = 1", userId);
    }

    @Override
    public boolean updateTaskStatus(String taskId, Integer status, String updatedBy) {
        CityTask task = cityTaskDao.get(taskId);
        if (task != null) {
            task.setStatus(status);
            task.setUpdatedBy(updatedBy);
            cityTaskDao.update(task);
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Object> generateDailyStatistics(Date date) {
        return new HashMap<>();
    }

    @Override
    public int countActiveTasksByAssignee(String userId) {
        String hql = "select count(*) from CityTask where assignedUserId = ?0 and status = 1";
        Long count = (Long) cityTaskDao.findUnique(hql, userId);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public List<CityTask> findUrgentTasks(String userId) {
        return cityTaskDao.find("from CityTask where assignedUserId = ?0 and priority = 4 and status = 1", userId);
    }

    @Override
    public boolean hasTaskPermission(String userId, String taskId, String permission) {
        CityTask task = cityTaskDao.get(taskId);
        return task != null && task.getAssignedUserId().equals(userId);
    }
}