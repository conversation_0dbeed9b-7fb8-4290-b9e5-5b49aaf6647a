package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.UserGridRole;

import java.util.List;

/**
 * 用户网格角色权限DAO接口
 * 
 * <AUTHOR> Team
 */
public interface UserGridRoleDao extends BaseDao<UserGridRole, String> {

    Page<UserGridRole> findByCondition(Page<UserGridRole> page, String userId, String gridCode,
                                       String roleName, Integer status);

    List<UserGridRole> findByUserId(String userId);

    List<UserGridRole> findByGridCode(String gridCode);

    UserGridRole findByUserIdAndGridCode(String userId, String gridCode);

    List<UserGridRole> findByRoleType(String roleType);

    UserGridRole findByUserIdAndGridCodeAndRole(String userId, String gridCode, String roleName);

    List<UserGridRole> findUsersByGridAndRole(String gridCode, String roleName);

    List<UserGridRole> findGridsByUserAndRole(String userId, String roleName);

    boolean hasPermission(String userId, String gridCode, String permission);

    String getUserPermissions(String userId, String gridCode);

    List<UserGridRole> findActiveRolesByUser(String userId);

    List<UserGridRole> findExpiredRoles();

    Page<UserGridRole> findRolesByGrid(Page<UserGridRole> page, String gridCode, String roleName, Integer status);

    List<UserGridRole> findRolesByUserAndStatus(String userId, Integer status);

    Page<UserGridRole> findRolesByUser(Page<UserGridRole> page, String userId, String gridCode, Integer status);
}
