package net.diaowen.dwsurvey.common;

import net.diaowen.dwsurvey.config.DWSurveyConfig;

public class FooterInfo {

    private String versionInfo;
    private String versionNumber;
    private String versionBuilt;

    private String siteName;
    private String siteUrl;
    private String siteMail;
    private String siteIcp;
    private String sitePhone;
    private String years;

    private String siteStatus;

    private String layoutType;

    public String getVersionInfo() {
        return versionInfo;
    }

    public void setVersionInfo(String versionInfo) {
        this.versionInfo = versionInfo;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getVersionBuilt() {
        return versionBuilt;
    }

    public void setVersionBuilt(String versionBuilt) {
        this.versionBuilt = versionBuilt;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public String getSiteMail() {
        return siteMail;
    }

    public void setSiteMail(String siteMail) {
        this.siteMail = siteMail;
    }

    public String getSiteIcp() {
        return siteIcp;
    }

    public void setSiteIcp(String siteIcp) {
        this.siteIcp = siteIcp;
    }

    public String getSitePhone() {
        return sitePhone;
    }

    public void setSitePhone(String sitePhone) {
        this.sitePhone = sitePhone;
    }

    public String getYears() {
        return years;
    }

    public void setYears(String years) {
        this.years = years;
    }

    public String getSiteStatus() {
        return siteStatus;
    }

    public void setSiteStatus(String siteStatus) {
        this.siteStatus = siteStatus;
    }

    public String getLayoutType() {
        return layoutType;
    }

    public void setLayoutType(String layoutType) {
        this.layoutType = layoutType;
    }
}
