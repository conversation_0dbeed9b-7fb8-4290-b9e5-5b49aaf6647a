package net.diaowen.common.base.entity;

import java.io.Serializable;

public class Result implements Serializable {

    private static final long serialVersionUID = 1L;

    private boolean success = true;
    private String msg = "操作成功";
    private Object data = null;

    public Result() {
    }

    public static Result ok() {
        return new Result();
    }

    public static Result ok(Object data) {
        Result r = new Result();
        r.setData(data);
        return r;
    }
    
    public static Result error(String msg) {
        Result r = new Result();
        r.setSuccess(false);
        r.setMsg(msg);
        return r;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}