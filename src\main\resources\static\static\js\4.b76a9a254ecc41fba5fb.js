(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{"37QJ":function(t,e,a){"use strict";a.d(e,"a",function(){return i}),a.d(e,"b",function(){return l});var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{"background-color":"white"}},[i("div",{staticStyle:{padding:"20px"}},[t._m(0),t._v(" "),i("div",[i("div",{staticStyle:{width:"550px"}},[i("el-form",{ref:"form",attrs:{model:t.form,"label-width":"130px","label-position":"left"}},[i("div",{staticStyle:{padding:"0 10px"}},[i("el-form-item",{attrs:{label:"站点名称"}},[i("el-input",{model:{value:t.form.siteName,callback:function(e){t.$set(t.form,"siteName",e)},expression:"form.siteName"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"站点URL"}},[i("el-input",{model:{value:t.form.siteUrl,callback:function(e){t.$set(t.form,"siteUrl",e)},expression:"form.siteUrl"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"站点备案号"}},[i("el-input",{model:{value:t.form.siteIcp,callback:function(e){t.$set(t.form,"siteIcp",e)},expression:"form.siteIcp"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{model:{value:t.form.siteAdminName,callback:function(e){t.$set(t.form,"siteAdminName",e)},expression:"form.siteAdminName"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"联系邮箱"}},[i("el-input",{model:{value:t.form.siteMail,callback:function(e){t.$set(t.form,"siteMail",e)},expression:"form.siteMail"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"联系电话"}},[i("el-input",{model:{value:t.form.sitePhone,callback:function(e){t.$set(t.form,"sitePhone",e)},expression:"form.sitePhone"}})],1)],1),t._v(" "),i("div",{staticStyle:{padding:"0 10px"}},[i("el-form-item",{attrs:{label:"LOGO",disabled:""}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"需要修改请升级到企业版",placement:"right"}},[i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-start","justify-items":"center"}},[i("div",[i("el-upload",{staticClass:"avatar-uploader",attrs:{action:"/api/dwsurvey/up/up-file.do","before-upload":t.handleExceed,"show-file-list":!1}},[t.form.logoImageUrl?i("img",{staticClass:"avatar",attrs:{src:t.form.logoImageUrl}}):i("img",{staticClass:"avatar",attrs:{src:a("zwU1")}})])],1),t._v(" "),i("div",{staticStyle:{"margin-left":"10px"}},[i("el-link",{staticStyle:{"line-height":"15px"},attrs:{href:"https://www.diaowen.net?s0=oss&v1=2501&e5=sfl0",type:"warning"}},[t._v("购买升级")])],1)])]),t._v(" "),i("div",{staticStyle:{"font-size":"13px",color:"gray","line-height":"13px","margin-top":"10px"}},[t._v("\n                点击图片上传新LOGO\n              ")])],1),t._v(" "),i("el-form-item",{attrs:{label:"底部版权信息",disabled:""}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"需要修改请升级到企业版",placement:"right"}},[i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-start"}},[i("el-switch",{attrs:{"active-text":"隐藏","inactive-text":"显示",disabled:""},model:{value:t.form.footerHide,callback:function(e){t.$set(t.form,"footerHide",e)},expression:"form.footerHide"}}),t._v(" "),i("div",{staticStyle:{"margin-left":"10px"}},[i("el-link",{staticStyle:{"line-height":"15px"},attrs:{href:"https://www.diaowen.net?s0=oss&v1=2501&e5=sfh5",type:"warning"}},[t._v("购买升级")])],1)],1)])],1),t._v(" "),i("el-form-item",{attrs:{label:"底部版权内容",disabled:""}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"需要修改请升级到企业版",placement:"right"}},[i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-start"}},[i("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"Powered by DWSurvey",disabled:""},model:{value:t.form.footerContent,callback:function(e){t.$set(t.form,"footerContent",e)},expression:"form.footerContent"}}),t._v(" "),i("div",{staticStyle:{"margin-left":"10px"}},[i("el-link",{staticStyle:{"line-height":"15px"},attrs:{href:"https://www.diaowen.net?s0=oss&v1=2501&e5=sfc3",type:"warning"}},[t._v("购买升级")])],1)],1)])],1)],1),t._v(" "),i("div",{staticStyle:{"margin-top":"10px"}},[i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("保存更新")]),t._v(" "),i("el-button",[t._v("取消")])],1)],1)])],1)])])])},l=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("h3",[this._v("系统配置")])])}]},ArtL:function(t,e,a){"use strict";a.r(e);var i=a("OU3A"),l=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);var s=a("NCZV"),o=a("JFUb"),n=Object(o.a)(l.a,s.a,s.b,!1,function(t){a("dPDm")},"data-v-d26a1a60",null);e.default=n.exports},NCZV:function(t,e,a){"use strict";a.d(e,"a",function(){return i}),a.d(e,"b",function(){return l});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-row",[a("el-col",{attrs:{span:24,offset:0}},[a("div",{staticClass:"dw-table-form",staticStyle:{"padding-left":"60px"}},[a("el-form",{staticClass:"dw-form-inline",attrs:{inline:!0,model:t.formInline,size:"medium"}},[a("el-form-item",{attrs:{label:"登录账号"}},[a("el-input",{attrs:{placeholder:"请输入查询的账号关键字",clearable:""},model:{value:t.formInline.loginName,callback:function(e){t.$set(t.formInline,"loginName",e)},expression:"formInline.loginName"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-left":"40px"},attrs:{label:"账号状态"}},[a("el-select",{attrs:{placeholder:"请选择账号状态",clearable:""},model:{value:t.formInline.status,callback:function(e){t.$set(t.formInline,"status",e)},expression:"formInline.status"}},[a("el-option",{attrs:{label:"不可用",value:"0"}}),t._v(" "),a("el-option",{attrs:{label:"未激活",value:"1"}}),t._v(" "),a("el-option",{attrs:{label:"激活",value:"2"}})],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-left":"40px"}},[a("el-button",{on:{click:t.onSubmit}},[t._v("重置")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("查询")])],1)],1)],1),t._v(" "),a("div",{staticClass:"dw-table"},[a("div",{staticClass:"dw-table-title"},[a("el-row",{attrs:{span:24,type:"flex",justify:"space-between",align:"middle"}},[a("el-col",{attrs:{span:4}},[a("h3",[t._v("用户管理")])]),t._v(" "),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:20}},[a("el-button",{directives:[{name:"has-dw-role",rawName:"v-has-dw-role",value:"DWSURVEY_SUPER_ADMIN",expression:"'DWSURVEY_SUPER_ADMIN'"}],attrs:{type:"primary",size:"medium"},on:{click:function(e){return t.openCreateUser()}}},[t._v("添加用户")])],1)],1)],1),t._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{label:"登录名"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{trigger:"hover",placement:"top"}},[a("p",{domProps:{innerHTML:t._s(e.row.loginName)}}),t._v(" "),a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[a("div",{domProps:{innerHTML:t._s(e.row.loginName)}})])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(e.row.createTime))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.status?a("el-tag",{attrs:{type:"danger"}},[t._v("不可用")]):1===e.row.status?a("el-tag",{attrs:{type:"info"}},[t._v("未激活")]):2===e.row.status?a("el-tag",{attrs:{type:"success"}},[t._v("激活")]):a("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{"disable-transitions":""}},[t._v("未知")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"登录时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("i",{staticClass:"el-icon-time"}),t._v(" "),a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(e.row.lastLoginTime))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button-group",[a("el-tooltip",{attrs:{effect:"dark",content:"编辑问卷",placement:"top"}},[a("el-button",{attrs:{size:"mini",content:"编辑",icon:"el-icon-edit"},on:{click:function(a){return t.handleEdit(e.$index,e.row)}}})],1),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",content:"删除问卷",placement:"top"}},[a("el-button",{attrs:{size:"mini",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.$index,e.row)}}})],1)],1)]}}])})],1),t._v(" "),a("div",{staticClass:"dw-pagination"},[a("el-pagination",{attrs:{"page-size":t.pageSize,"current-page":t.currentPage,total:t.total,background:"",layout:"prev, pager, next"},on:{"current-change":t.handleCurrentChange}})],1)],1)])],1),t._v(" "),a("div",[a("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogFormVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"userForm",attrs:{model:t.userForm,rules:t.userFormRules,"status-icon":"","label-position":"top"}},[a("el-form-item",{staticStyle:{"margin-top":"0px"},attrs:{"label-width":t.formLabelWidth,label:"设置账号",prop:"loginName"}},[a("el-input",{attrs:{autocomplete:"off",placeholder:"请设置登录账号","show-word-limit":""},model:{value:t.userForm.loginName,callback:function(e){t.$set(t.userForm,"loginName",e)},expression:"userForm.loginName"}})],1),t._v(" "),a("el-form-item",{staticClass:"dw-dialog-form-item",attrs:{"label-width":t.formLabelWidth,label:"账号状态",prop:"status"}},[a("el-radio-group",{model:{value:t.userForm.status,callback:function(e){t.$set(t.userForm,"status",e)},expression:"userForm.status"}},[a("el-radio",{attrs:{label:0}},[t._v("不可用")]),t._v(" "),a("el-radio",{attrs:{label:1}},[t._v("未激活")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("激活")])],1)],1),t._v(" "),a("el-form-item",{staticClass:"dw-dialog-form-item",attrs:{"label-width":t.formLabelWidth,label:"设置密码",prop:"pwd"}},[a("el-input",{attrs:{autocomplete:"off",placeholder:"新建时必须设置密码，修改时不设置代表不修改。","show-password":""},model:{value:t.userForm.pwd,callback:function(e){t.$set(t.userForm,"pwd",e)},expression:"userForm.pwd"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSaveUser()}}},[t._v("确 定")])],1)],1)],1)],1)},l=[]},OU3A:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("QtE3");e.default={name:"AdminUserList",data:function(){return{tableData:[],pageSize:10,currentPage:1,total:0,formInline:{loginName:null,status:null},dialogTitle:"创建用户",dialogFormVisible:!1,userForm:{id:null,loginName:"",pwd:"",status:2},userFormRules:{loginName:[{required:!0,message:"请输入登录账号",trigger:"blur"},{min:6,max:18,message:"长度在 6 到 18 个字符",trigger:"blur"}],pwd:[{required:!0,message:"请输入登录密码",trigger:"blur"},{min:6,max:18,message:"长度在 6 到 18 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择账号状态",trigger:"change"}]},formLabelWidth:"120px"}},mounted:function(){this.queryList(1)},methods:{buttonClickA:function(t){window.location.href=t},handlePush:function(t){this.$router.push(t)},onSubmit:function(){console.log("submit!"),this.queryList(1)},handleCurrentChange:function(t){this.queryList(t)},openCreateUser:function(){this.dialogTitle="创建用户",this.dialogFormVisible=!0,this.userFormRules.pwd=[{required:!0,message:"请输入登录密码",trigger:"blur"},{min:6,max:18,message:"长度在 6 到 18 个字符",trigger:"blur"}],this.userForm.id=null},queryList:function(t){var e=this,a=this.formInline,l=a.status,r=a.loginName;(0,i.dwAdminUserList)(this.pageSize,t,l,r).then(function(t){var a=t.data.data;e.tableData=a,e.total=t.data.total,e.currentPage=t.data.current,e.pageSize=t.data.pageSize})},handleEdit:function(t,e){this.dialogTitle="编辑用户",this.dialogFormVisible=!0,this.userFormRules.pwd={required:!1},this.userForm.loginName=e.loginName,this.userForm.status=e.status,this.userForm.id=e.id,console.log(t,e)},handleDelete:function(t,e){var a=this;console.log(t,e),this.$msgbox.confirm("确认删除此用户吗？","删除警告",{type:"warning",confirmButtonText:"确认删除"}).then(function(){var t={id:[e.id]};(0,i.dwUserDelete)(t).then(function(t){200===t.data.resultCode?(a.$message.success("删除成功，即将刷新数据。"),a.queryList(1)):a.$message.error("添加用户失败")})}).catch(function(){})},handleSaveUser:function(){var t=this;this.$refs.userForm.validate(function(e){if(!e)return console.log("error submit!!"),!1;var a=t.userForm,l=a.id,r=a.loginName,s=a.pwd,o=a.status;if(null===l){var n={loginName:r,pwd:s,status:o};(0,i.dwUserCreate)(n).then(function(e){var a=e.data;200===a.resultCode?(t.$message.success("添加成功，即将刷新数据。"),t.queryList(1),t.dialogFormVisible=!1):t.$message.error("添加用户失败"+a.data)})}else{var u={id:l,loginName:r,pwd:s,status:o};(0,i.dwUserUpdate)(u).then(function(e){var a=e.data;200===a.resultCode?(t.$message.success("修改成功，即将刷新数据。"),t.queryList(1),t.dialogFormVisible=!1):t.$message.error("添加用户失败"+a.data)})}})}}}},QtE3:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.dwAdminUserList=function(t,e,a,r){var s={pageSize:t,current:e,status:a,loginName:r};return(0,i.default)({url:l.default.adminUserList,method:"get",params:s})},e.dwUserCreate=function(t){return(0,i.default)({url:l.default.adminUserCreate,method:"post",data:t})},e.dwUserUpdate=function(t){return(0,i.default)({url:l.default.adminUserUpdate,method:"put",data:t})},e.dwUserDelete=function(t){return(0,i.default)({url:l.default.adminUserDelete,method:"delete",data:t})};var i=r(a("t3Un")),l=r(a("Nlzp"));function r(t){return t&&t.__esModule?t:{default:t}}},W1pz:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={name:"DwSystemSet",data:function(){return{form:{siteName:"调问网",siteUrl:"http://www.diaowen.net",siteIcp:"京ICP备13050030号-3",siteMail:"<EMAIL>",siteAdminName:"管理员",sitePhone:18888888888,logoImageUrl:"",footerHide:!1,footerContent:"Powered by DWSurvey"}}},methods:{handleExceed:function(t,e){return this.$message.warning("当前社区版，不支持修改系统默认LOGO，如需修改请升级到企业版"),!1},onSubmit:function(){console.log("submit!")}}}},dPDm:function(t,e,a){},doOE:function(t,e,a){"use strict";a.r(e);var i=a("W1pz"),l=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);var s=a("37QJ"),o=a("JFUb"),n=Object(o.a)(l.a,s.a,s.b,!1,function(t){a("mWuf")},null,null);e.default=n.exports},mWuf:function(t,e,a){}}]);