package net.diaowen.dwsurvey.service;

import net.diaowen.dwsurvey.entity.CityTask;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 任务推送服务
 * 实现WebSocket实时任务推送功能
 * <AUTHOR> Team
 */
@Service
public class TaskPushService {

    // 存储用户WebSocket会话
    private final Map<String, List<WebSocketSession>> userSessions = new ConcurrentHashMap<>();
    
    // 存储网格WebSocket会话
    private final Map<String, List<WebSocketSession>> gridSessions = new ConcurrentHashMap<>();

    /**
     * 添加用户会话
     */
    public void addUserSession(String userId, WebSocketSession session) {
        userSessions.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(session);
    }

    /**
     * 移除用户会话
     */
    public void removeUserSession(String userId, WebSocketSession session) {
        List<WebSocketSession> sessions = userSessions.get(userId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                userSessions.remove(userId);
            }
        }
    }

    /**
     * 添加网格会话
     */
    public void addGridSession(String gridId, WebSocketSession session) {
        gridSessions.computeIfAbsent(gridId, k -> new CopyOnWriteArrayList<>()).add(session);
    }

    /**
     * 移除网格会话
     */
    public void removeGridSession(String gridId, WebSocketSession session) {
        List<WebSocketSession> sessions = gridSessions.get(gridId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                gridSessions.remove(gridId);
            }
        }
    }

    /**
     * 向指定用户推送任务
     */
    public void pushTaskToUser(String userId, CityTask task, String messageType) {
        List<WebSocketSession> sessions = userSessions.get(userId);
        if (sessions != null && !sessions.isEmpty()) {
            String message = buildTaskMessage(task, messageType);
            for (WebSocketSession session : sessions) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(message));
                    } catch (IOException e) {
                        e.printStackTrace();
                        // 发送失败，移除会话
                        sessions.remove(session);
                    }
                }
            }
        }
    }

    /**
     * 向网格内所有用户推送任务
     */
    public void pushTaskToGrid(String gridId, CityTask task, String messageType) {
        List<WebSocketSession> sessions = gridSessions.get(gridId);
        if (sessions != null && !sessions.isEmpty()) {
            String message = buildTaskMessage(task, messageType);
            for (WebSocketSession session : sessions) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(message));
                    } catch (IOException e) {
                        e.printStackTrace();
                        // 发送失败，移除会话
                        sessions.remove(session);
                    }
                }
            }
        }
    }

    /**
     * 向多个用户推送任务
     */
    public void pushTaskToUsers(List<String> userIds, CityTask task, String messageType) {
        for (String userId : userIds) {
            pushTaskToUser(userId, task, messageType);
        }
    }

    /**
     * 推送紧急任务
     */
    public void pushUrgentTask(CityTask task) {
        // 向任务所属网格的所有用户推送紧急任务
        pushTaskToGrid(task.getGridId(), task, "URGENT_TASK");
        
        // 如果有指定的负责人，单独推送
        if (task.getAssigneeId() != null) {
            pushTaskToUser(task.getAssigneeId(), task, "URGENT_TASK");
        }
    }

    /**
     * 推送任务状态更新
     */
    public void pushTaskStatusUpdate(CityTask task) {
        // 向任务负责人推送状态更新
        if (task.getAssigneeId() != null) {
            pushTaskToUser(task.getAssigneeId(), task, "TASK_STATUS_UPDATE");
        }
        
        // 向任务创建者推送状态更新
        if (task.getCreatorId() != null && !task.getCreatorId().equals(task.getAssigneeId())) {
            pushTaskToUser(task.getCreatorId(), task, "TASK_STATUS_UPDATE");
        }
    }

    /**
     * 推送新任务分配
     */
    public void pushNewTaskAssignment(CityTask task) {
        if (task.getAssigneeId() != null) {
            pushTaskToUser(task.getAssigneeId(), task, "NEW_TASK_ASSIGNMENT");
        }
    }

    /**
     * 推送任务提醒
     */
    public void pushTaskReminder(CityTask task) {
        if (task.getAssigneeId() != null) {
            pushTaskToUser(task.getAssigneeId(), task, "TASK_REMINDER");
        }
    }

    /**
     * 推送任务截止提醒
     */
    public void pushTaskDeadlineReminder(CityTask task) {
        if (task.getAssigneeId() != null) {
            pushTaskToUser(task.getAssigneeId(), task, "TASK_DEADLINE_REMINDER");
        }
    }

    /**
     * 构建任务消息
     */
    private String buildTaskMessage(CityTask task, String messageType) {
        StringBuilder message = new StringBuilder();
        message.append("{");
        message.append("\"type\":\"").append(messageType).append("\",");
        message.append("\"taskId\":\"").append(task.getId()).append("\",");
        message.append("\"taskTitle\":\"").append(task.getTitle()).append("\",");
        message.append("\"taskType\":\"").append(task.getTaskType()).append("\",");
        message.append("\"priority\":").append(task.getPriority()).append(",");
        message.append("\"status\":\"").append(task.getStatus()).append("\",");
        message.append("\"gridId\":\"").append(task.getGridId()).append("\",");
        message.append("\"assigneeId\":\"").append(task.getAssigneeId() != null ? task.getAssigneeId() : "").append("\",");
        message.append("\"deadline\":\"").append(task.getDeadline() != null ? task.getDeadline().toString() : "").append("\",");
        message.append("\"timestamp\":").append(System.currentTimeMillis());
        message.append("}");
        return message.toString();
    }

    /**
     * 广播系统消息
     */
    public void broadcastSystemMessage(String message) {
        // 向所有在线用户广播系统消息
        for (List<WebSocketSession> sessions : userSessions.values()) {
            for (WebSocketSession session : sessions) {
                if (session.isOpen()) {
                    try {
                        String systemMessage = "{\"type\":\"SYSTEM_MESSAGE\",\"message\":\"" + message + "\",\"timestamp\":" + System.currentTimeMillis() + "}";
                        session.sendMessage(new TextMessage(systemMessage));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * 获取活跃网格数量
     */
    public int getActiveGridCount() {
        return gridSessions.size();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(String userId) {
        List<WebSocketSession> sessions = userSessions.get(userId);
        if (sessions != null) {
            // 移除已关闭的会话
            sessions.removeIf(session -> !session.isOpen());
            return !sessions.isEmpty();
        }
        return false;
    }

    /**
     * 清理已关闭的会话
     */
    public void cleanupClosedSessions() {
        // 清理用户会话
        userSessions.entrySet().removeIf(entry -> {
            List<WebSocketSession> sessions = entry.getValue();
            sessions.removeIf(session -> !session.isOpen());
            return sessions.isEmpty();
        });

        // 清理网格会话
        gridSessions.entrySet().removeIf(entry -> {
            List<WebSocketSession> sessions = entry.getValue();
            sessions.removeIf(session -> !session.isOpen());
            return sessions.isEmpty();
        });
    }
}