package net.diaowen.dwsurvey.controller.task;

import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.service.TaskGridManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务网格管理控制器
 * <AUTHOR> Team
 */
@Controller
@RequestMapping("/api/task-grid")
public class TaskGridController {

    @Autowired
    private TaskGridManager taskGridManager;
    
    @Autowired
    private AccountManager accountManager;

    /**
     * 获取网格列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridList(HttpServletRequest request,
                                  @RequestParam(defaultValue = "1") Integer page,
                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                  @RequestParam(required = false) String keyword,
                                  @RequestParam(required = false) String parentId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            Page<TaskGrid> gridPage = new Page<>(page, pageSize);
            gridPage = taskGridManager.findByCondition(gridPage, keyword, parentId);

            Map<String, Object> result = new HashMap<>();
            result.put("grids", gridPage.getResult());
            result.put("total", gridPage.getTotalItems());
            result.put("page", page);
            result.put("pageSize", pageSize);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格详情
     */
    @RequestMapping(value = "/{gridId}", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridDetail(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            TaskGrid grid = taskGridManager.getEntity(gridId);
            if (grid == null) {
                return HttpResult.FAILURE("网格不存在");
            }

            return HttpResult.SUCCESS(grid);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建网格
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult createGrid(@RequestBody TaskGrid grid) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            grid.setCreatedBy(user.getId());
            taskGridManager.save(grid);

            return HttpResult.SUCCESS("网格创建成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("创建网格失败：" + e.getMessage());
        }
    }

    /**
     * 更新网格
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult updateGrid(@RequestBody TaskGrid grid) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            grid.setUpdatedBy(user.getId());
            taskGridManager.save(grid);

            return HttpResult.SUCCESS("网格更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("更新网格失败：" + e.getMessage());
        }
    }

    /**
     * 删除网格
     */
    @RequestMapping(value = "/delete/{gridId}", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult deleteGrid(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            taskGridManager.delete(gridId);

            return HttpResult.SUCCESS("网格删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("删除网格失败：" + e.getMessage());
        }
    }

    /**
     * 获取子网格
     */
    @RequestMapping(value = "/{parentId}/children", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getChildGrids(@PathVariable String parentId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> childGrids = taskGridManager.findByParentId(parentId);

            return HttpResult.SUCCESS(childGrids);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取子网格失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格树
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridTree(@RequestParam(required = false) String rootId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> gridTree = taskGridManager.buildGridTree(rootId);

            return HttpResult.SUCCESS(gridTree);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格树失败：" + e.getMessage());
        }
    }

    /**
     * 根据区域查找网格
     */
    @RequestMapping(value = "/area/{areaCode}", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridsByArea(@PathVariable String areaCode) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> grids = taskGridManager.findByArea(areaCode);

            return HttpResult.SUCCESS(grids);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("根据区域查找网格失败：" + e.getMessage());
        }
    }

    /**
     * 根据边界查找网格
     */
    @RequestMapping(value = "/bounds", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridsByBounds(@RequestParam Double minLng,
                                       @RequestParam Double minLat,
                                       @RequestParam Double maxLng,
                                       @RequestParam Double maxLat) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> grids = taskGridManager.findGridsByBounds(minLng, minLat, maxLng, maxLat);

            return HttpResult.SUCCESS(grids);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("根据边界查找网格失败：" + e.getMessage());
        }
    }

    /**
     * 更新网格状态
     */
    @RequestMapping(value = "/status", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult updateGridStatus(@RequestParam String gridId, @RequestParam Integer status) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            taskGridManager.updateStatus(gridId, status);

            return HttpResult.SUCCESS("网格状态更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("更新网格状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新网格状态
     */
    @RequestMapping(value = "/batch/status", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult batchUpdateGridStatus(@RequestBody Map<String, Object> params) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            @SuppressWarnings("unchecked")
            List<String> gridIds = (List<String>) params.get("gridIds");
            Integer status = (Integer) params.get("status");

            if (gridIds == null || gridIds.isEmpty()) {
                return HttpResult.FAILURE("网格ID列表不能为空");
            }

            taskGridManager.batchUpdateStatus(gridIds, status);

            return HttpResult.SUCCESS("批量更新网格状态成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("批量更新网格状态失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否有子网格
     */
    @RequestMapping(value = "/{gridId}/has-children", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult hasChildGrids(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            boolean hasChildren = taskGridManager.hasChildGrids(gridId);

            Map<String, Object> result = new HashMap<>();
            result.put("hasChildren", hasChildren);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("检查子网格失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格统计
     */
    @RequestMapping(value = "/stats", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridStats() {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("level1Count", taskGridManager.countByLevel(1));
            stats.put("level2Count", taskGridManager.countByLevel(2));
            stats.put("level3Count", taskGridManager.countByLevel(3));
            stats.put("level4Count", taskGridManager.countByLevel(4));

            return HttpResult.SUCCESS(stats);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格路径
     */
    @RequestMapping(value = "/{gridId}/path", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridPath(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> path = taskGridManager.getGridPath(gridId);

            return HttpResult.SUCCESS(path);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格路径失败：" + e.getMessage());
        }
    }

    /**
     * 搜索网格
     */
    @RequestMapping(value = "/search", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult searchGrids(@RequestParam String keyword,
                                  @RequestParam(defaultValue = "1") Integer page,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            Page<TaskGrid> gridPage = new Page<>(page, pageSize);
            gridPage = taskGridManager.searchGrids(gridPage, keyword);

            Map<String, Object> result = new HashMap<>();
            result.put("grids", gridPage.getResult());
            result.put("total", gridPage.getTotalItems());
            result.put("page", page);
            result.put("pageSize", pageSize);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("搜索网格失败：" + e.getMessage());
        }
    }

    /**
     * 获取附近网格
     */
    @RequestMapping(value = "/nearby", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getNearbyGrids(@RequestParam Double longitude,
                                     @RequestParam Double latitude,
                                     @RequestParam(defaultValue = "1000") Double radius) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<TaskGrid> nearbyGrids = taskGridManager.findNearbyGrids(longitude, latitude, radius);

            return HttpResult.SUCCESS(nearbyGrids);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取附近网格失败：" + e.getMessage());
        }
    }
}