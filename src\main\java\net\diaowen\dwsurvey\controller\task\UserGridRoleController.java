package net.diaowen.dwsurvey.controller.task;

import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.UserGridRole;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户网格角色管理控制器
 * <AUTHOR> Team
 */
@Controller
@RequestMapping("/api/user-grid-role")
public class UserGridRoleController {

    @Autowired
    private UserGridRoleManager userGridRoleManager;
    
    @Autowired
    private AccountManager accountManager;

    /**
     * 获取用户网格角色列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getRoleList(HttpServletRequest request,
                                  @RequestParam(defaultValue = "1") Integer page,
                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                  @RequestParam(required = false) String userId,
                                  @RequestParam(required = false) String gridId,
                                  @RequestParam(required = false) String roleCode,
                                  @RequestParam(required = false) Integer status) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            Page<UserGridRole> rolePage = new Page<>(page, pageSize);
            rolePage = userGridRoleManager.findByCondition(rolePage, userId, gridId, roleCode, status);

            Map<String, Object> result = new HashMap<>();
            result.put("roles", rolePage.getResult());
            result.put("total", rolePage.getTotalItems());
            result.put("page", page);
            result.put("pageSize", pageSize);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取用户网格角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的网格角色
     */
    @RequestMapping(value = "/user/{userId}", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getUserRoles(@PathVariable String userId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<UserGridRole> userRoles = userGridRoleManager.findByUserId(userId);

            return HttpResult.SUCCESS(userRoles);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取用户网格角色失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格的用户角色
     */
    @RequestMapping(value = "/grid/{gridId}", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridRoles(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<UserGridRole> gridRoles = userGridRoleManager.findByGridId(gridId);

            return HttpResult.SUCCESS(gridRoles);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格用户角色失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户负责的网格ID列表
     */
    @RequestMapping(value = "/user/{userId}/grids", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getUserGrids(@PathVariable String userId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<String> gridIds = userGridRoleManager.findGridIdsByUserId(userId);

            return HttpResult.SUCCESS(gridIds);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取用户网格失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格的用户ID列表
     */
    @RequestMapping(value = "/grid/{gridId}/users", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridUsers(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<String> userIds = userGridRoleManager.findUserIdsByGridId(gridId);

            return HttpResult.SUCCESS(userIds);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格用户失败：" + e.getMessage());
        }
    }

    /**
     * 分配用户到网格
     */
    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult assignUserToGrid(@RequestParam String userId,
                                       @RequestParam String gridId,
                                       @RequestParam String roleCode,
                                       @RequestParam(required = false) String permissions) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            userGridRoleManager.assignUserToGrid(userId, gridId, roleCode, permissions);

            return HttpResult.SUCCESS("用户分配成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("分配用户失败：" + e.getMessage());
        }
    }

    /**
     * 从网格移除用户
     */
    @RequestMapping(value = "/remove", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult removeUserFromGrid(@RequestParam String userId, @RequestParam String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            userGridRoleManager.removeUserFromGrid(userId, gridId);

            return HttpResult.SUCCESS("用户移除成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("移除用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户角色
     */
    @RequestMapping(value = "/role", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult updateUserRole(@RequestParam String userId,
                                     @RequestParam String gridId,
                                     @RequestParam String roleCode) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            userGridRoleManager.updateUserRole(userId, gridId, roleCode);

            return HttpResult.SUCCESS("用户角色更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("更新用户角色失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户权限
     */
    @RequestMapping(value = "/permissions", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult updateUserPermissions(@RequestParam String userId,
                                            @RequestParam String gridId,
                                            @RequestParam String permissions) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            userGridRoleManager.updateUserPermissions(userId, gridId, permissions);

            return HttpResult.SUCCESS("用户权限更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("更新用户权限失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户权限
     */
    @RequestMapping(value = "/check-permission", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult checkPermission(@RequestParam String userId,
                                      @RequestParam String gridId,
                                      @RequestParam String permission) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            boolean hasPermission = userGridRoleManager.hasPermission(userId, gridId, permission);

            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("检查用户权限失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格管理员
     */
    @RequestMapping(value = "/grid/{gridId}/managers", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridManagers(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<UserGridRole> managers = userGridRoleManager.findManagersByGrid(gridId);

            return HttpResult.SUCCESS(managers);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格管理员失败：" + e.getMessage());
        }
    }

    /**
     * 获取网格数据采集员
     */
    @RequestMapping(value = "/grid/{gridId}/collectors", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getGridCollectors(@PathVariable String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<UserGridRole> collectors = userGridRoleManager.findCollectorsByGrid(gridId);

            return HttpResult.SUCCESS(collectors);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取网格数据采集员失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否为网格管理员
     */
    @RequestMapping(value = "/is-manager", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult isGridManager(@RequestParam String userId, @RequestParam String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            boolean isManager = userGridRoleManager.isGridManager(userId, gridId);

            Map<String, Object> result = new HashMap<>();
            result.put("isManager", isManager);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("检查管理员权限失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否为数据采集员
     */
    @RequestMapping(value = "/is-collector", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult isDataCollector(@RequestParam String userId, @RequestParam String gridId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            boolean isCollector = userGridRoleManager.isDataCollector(userId, gridId);

            Map<String, Object> result = new HashMap<>();
            result.put("isCollector", isCollector);

            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("检查采集员权限失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户管理的网格
     */
    @RequestMapping(value = "/user/{userId}/managed-grids", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getManagedGrids(@PathVariable String userId) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            List<String> managedGridIds = userGridRoleManager.findManagedGridIds(userId);

            return HttpResult.SUCCESS(managedGridIds);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取管理网格失败：" + e.getMessage());
        }
    }

    /**
     * 批量分配用户
     */
    @RequestMapping(value = "/batch/assign", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult batchAssignUsers(@RequestBody Map<String, Object> params) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            @SuppressWarnings("unchecked")
            List<String> userIds = (List<String>) params.get("userIds");
            String gridId = (String) params.get("gridId");
            String roleCode = (String) params.get("roleCode");
            String permissions = (String) params.get("permissions");

            if (userIds == null || userIds.isEmpty()) {
                return HttpResult.FAILURE("用户ID列表不能为空");
            }

            userGridRoleManager.batchAssignUsers(userIds, gridId, roleCode, permissions);

            return HttpResult.SUCCESS("批量分配用户成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("批量分配用户失败：" + e.getMessage());
        }
    }

    /**
     * 批量移除用户
     */
    @RequestMapping(value = "/batch/remove", method = RequestMethod.POST)
    @ResponseBody
    public HttpResult batchRemoveUsers(@RequestBody Map<String, Object> params) {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            @SuppressWarnings("unchecked")
            List<String> userIds = (List<String>) params.get("userIds");
            String gridId = (String) params.get("gridId");

            if (userIds == null || userIds.isEmpty()) {
                return HttpResult.FAILURE("用户ID列表不能为空");
            }

            userGridRoleManager.batchRemoveUsers(userIds, gridId);

            return HttpResult.SUCCESS("批量移除用户成功");
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("批量移除用户失败：" + e.getMessage());
        }
    }

    /**
     * 获取角色统计
     */
    @RequestMapping(value = "/stats", method = RequestMethod.GET)
    @ResponseBody
    public HttpResult getRoleStats() {
        try {
            User user = accountManager.getCurUser();
            if (user == null) {
                return HttpResult.FAILURE("用户未登录");
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("managerCount", userGridRoleManager.countByRole("GRID_MANAGER"));
            stats.put("adminCount", userGridRoleManager.countByRole("GRID_ADMIN"));
            stats.put("collectorCount", userGridRoleManager.countByRole("DATA_COLLECTOR"));

            return HttpResult.SUCCESS(stats);
        } catch (Exception e) {
            e.printStackTrace();
            return HttpResult.FAILURE("获取角色统计失败：" + e.getMessage());
        }
    }
}