dwsurvey:
  # 用户模式切换，暂且保持默认 local, test，demo
  site: 'local'
  web:
    # 后端文件存储目录，默认即在安装包同级目录下
    file-path: dwfile/dwsurvey/
    # 使用哪种部署模式，后期可能会去掉，可以先保持默认
    static-type: static
    # 站点访问地址，如果配置有域名则为域名地址
    site-url: http://localhost:8081
    # 资源访问地址，便于存储模式的切换，目前可以保持与site-url一样
    resource-url: http://localhost:8081
    # 前端路由形式 createWebHashHistory = '/#', createWebHistory = ''
    router-history: '/#'
    # 站点信息配置，会出现在底部footer区
    info:
      # 站点名称
      site-name: 调问网
      # 站点名称，对应的URL地址
      site-url: http://************
      # 站点备案号
      site-icp: 京ICP备13050030号-3
      # 站点联系人邮箱
      site-mail: <EMAIL>
      # 站点联系人电话
      site-phone: 18888888888
  # 用于微信集成相关，目前社区版还没未启用。
    login-register:
      login:
        wx-login: false
        phone-login: true
        pwd-login: true
      register: true
  sms:
    code-open: false
    sendcloud:
      smsUser:
      smsKey:
  weixin:
    open: false
    app-id: x
    app-secret: x
    server:
      token:
      encodingAESKey:
  dw-open: # 预留配置
    api:
      status: disable # test=开发测试模式，不进行API验证；prod=生产模式；验证签名，disable=关闭API功能
      timestamp: 30 # 签名有效期
  file-storage:
    prefix:  #路径前缀，方便给所有资源加上统一的路径，如：/dws-storage
    type: local #存储方式 local=本地存储（会存储在dwsurvey.web.file-path），yun=使用云存储（默认提供阿里云存储实现,注意配置OSS BUCKET 跨域访问）
    aliyun:
      endpoint: https://oss-cn-beijing.aliyuncs.com
      access-keyid:
      secret-accesskey:
      bucket: # 注意打开云存方案的跨域权限
        prefix: dwsurvey
        web-pub: web-pub
        web-pri: web-pri
# 服务占用的端口号
server:
  port: 8080
  tomcat:
    max-http-form-post-size: -1

# Spring相关配置
spring:
  # json处理
  jackson:
    # 时间编码格式
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # spring web 配置
  web:
    resources:
      # 静态资源目录配置，注意这里引用了上面的 dwsurvey.web.file-path 配置
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${dwsurvey.web.file-path}
  # 数据库配置
  datasource:
    #type: com.alibaba.druid.pool.DruidDataSource
    # 连接URL
    url: *******************************************************************************
    # 连接账号
    username: root
    # 连接密码
    password: Yjcsxdl@2022
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      remove-abandoned-timeout-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      stat-view-servlet:
        enabled: false
        reset-enable: false
        #         访问黑名单
        deny: ***********
        #          白名单
        allow: 'localhost'
        url-pattern: /druid/*
        # 上生产环境一定要改账号与密码！
        login-username: admin
        login-password: dwsdb123456
  # jpa配置
  jpa:
    # 连接的数据库
    database: mysql
    generate-ddl: true
    show-sql: true
    hibernate:
      ddl-auto: update
      # naming:
      #  implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl #指定jpa的自动表生成策略，驼峰自动映射为下划线格式
    properties:
      hibernate:
        #是否自动打印hql对应的sql语句
        show_sql: false
        #是否格式化sql语句
        format_sql: false
        #事务交由spring管理
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
        #        enable_lazy_load_no_trans: true
        cache:
          use_second_level_cache: false
          use_query_cache: false
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    jedis:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 200
        max-wait: 3000
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
# 日志配置
logging:
  # 日志级别，具体大家可以根据实际情况来调整
  level:
    net:
      diaowen: DEBUG
    org:
      hibernate: ERROR
      apache: ERROR
elasticsearch:
  username: elastic
  passwd: A0ThxyWi1gMNJzv7iU7M
#  apikey: ==
  hosts: *************:9200 # 多个IP逗号隔开
#  cert-name: http_ca.crt
#  后加的ES参数
#  协议方式，使用证书这里改成https
  scheme: http
#  索引统一前缀，默认为空不加前缀, 如果加建议格式如：dw_
  index-prev: oss_
# 认证方式 noPwd, pwd, cert
  security: noPwd
springfox:
  documentation:
    enabled: true
